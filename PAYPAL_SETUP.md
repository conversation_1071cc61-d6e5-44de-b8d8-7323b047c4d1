# PayPal Integration Setup Guide

## Overview
ExpenseFlow now includes PayPal integration for premium one-time payment upgrades. This guide will help you set up PayPal for your application.

### Pricing Model
- **Free Tier**: Basic features with limitations
- **Premium Tier**: $5.00 one-time payment for lifetime access
- **No Subscriptions**: Simple one-time purchase, no recurring billing

## Prerequisites
- PayPal Developer account (create at https://developer.paypal.com)
- Supabase project with profiles table set up
- Next.js application running

## Step 1: Install PayPal Dependencies

```bash
npm install @paypal/react-paypal-js @paypal/paypal-js
```

## Step 2: PayPal Developer Dashboard Setup

### 2.1 Create Application
1. Go to your PayPal Developer Dashboard
2. Navigate to **My Apps & Credentials**
3. Click **Create App**
4. Choose **Default Application** type
5. Select **Sandbox** for testing or **Live** for production
6. Note your **Client ID** and **Client Secret**

### 2.2 Configure App Settings
1. In your app settings, ensure these features are enabled:
   - **Accept Payments**
   - **Log In with PayPal** (optional)
2. Set up return URLs:
   - Success URL: `https://yourdomain.com/subscription/success`
   - Cancel URL: `https://yourdomain.com/subscription?canceled=true`

## Step 3: Environment Variables

Update your `.env.local` file:

```env
# PayPal Configuration (One-time Payment)
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

## Step 4: Test the Integration

### 4.1 Test Mode (Sandbox)
- Use PayPal's sandbox environment for testing
- Create test accounts in PayPal Developer Dashboard
- Test with sandbox PayPal accounts

### 4.2 Test Flow
1. Sign in to your app
2. Go to subscription page
3. Click PayPal button on Premium plan ($5.00 one-time)
4. Complete checkout with test PayPal account
5. Verify premium status is activated immediately

## Step 5: Production Deployment

### 5.1 Switch to Live Mode
1. Create a **Live** application in PayPal Developer Dashboard
2. Update environment variables with live client ID and secret
3. Update NEXT_PUBLIC_SITE_URL to production URL

### 5.2 Security Checklist
- ✅ API credentials are stored as environment variables
- ✅ User authentication is verified before creating orders
- ✅ Order capture is validated server-side
- ✅ Subscription status is updated securely

## Features Implemented

### ✅ One-Time Payment Management
- Create PayPal orders for premium upgrades ($5.00 one-time)
- Handle payment completion via PayPal SDK
- Update user premium status in Supabase immediately
- Automatic profile creation with default free tier

### ✅ User Experience
- PayPal button integration with loading states
- Success page after payment completion
- Error handling with user-friendly messages
- Pricing display with lifetime access ($5.00 one-time)

### ✅ Security
- Server-side user authentication verification
- Secure order creation and capture
- Row-level security in Supabase
- Secure API credential management

## API Endpoints

### POST `/api/paypal/create-order`
Creates a PayPal order for one-time premium upgrade.

**Request:**
```json
{
  "userId": "user-uuid"
}
```

**Response:**
```json
{
  "orderId": "order-id"
}
```

### POST `/api/paypal/capture-order`
Captures a PayPal order and updates user subscription.

**Request:**
```json
{
  "orderId": "order-id",
  "userId": "user-uuid"
}
```

**Response:**
```json
{
  "success": true,
  "captureData": {...}
}
```

## Troubleshooting

### Common Issues

1. **PayPal button not loading**
   - Check client ID is correct
   - Verify environment variables are set
   - Check browser console for errors

2. **Order creation fails**
   - Verify API credentials are correct
   - Check user authentication
   - Ensure PayPal app is properly configured

3. **Premium status not updating**
   - Check order capture response
   - Verify Supabase connection
   - Check user ID mapping in order

### Testing Locally
Use PayPal's sandbox environment for local testing with test accounts.

## Key Differences from Stripe

| Feature | Stripe | PayPal ✅ |
|---------|--------|-----------|
| **Setup** | Webhooks required | SDK-based |
| **Integration** | Redirect to checkout | Embedded buttons |
| **Testing** | Test card numbers | Sandbox accounts |
| **User Experience** | External checkout | Inline payment |
| **Fees** | 2.9% + 30¢ | 2.9% + 30¢ |

## Support
For issues with PayPal integration, check:
- PayPal Developer Dashboard logs
- Browser console errors
- Server logs for API calls
- Supabase logs for database operations

## Quick Start Checklist
- [ ] Install PayPal dependencies
- [ ] Create PayPal Developer app
- [ ] Get Client ID and Client Secret
- [ ] Update environment variables
- [ ] Test with sandbox accounts
- [ ] Deploy and configure live app
