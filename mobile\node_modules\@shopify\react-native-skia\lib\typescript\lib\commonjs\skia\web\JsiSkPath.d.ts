export const __esModule: boolean;
export class JsiSkPath extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    addPath(src: any, matrix: any, extend?: boolean): this;
    addArc(oval: any, startAngleInDegrees: any, sweepAngleInDegrees: any): this;
    addOval(oval: any, isCCW: any, startIndex: any): this;
    countPoints(): any;
    addPoly(points: any, close: any): this;
    moveTo(x: any, y: any): this;
    lineTo(x: any, y: any): this;
    makeAsWinding(): any;
    offset(dx: any, dy: any): this;
    rArcTo(rx: any, ry: any, xAxisRotateInDegrees: any, useSmallArc: any, isCCW: any, dx: any, dy: any): this;
    rConicTo(dx1: any, dy1: any, dx2: any, dy2: any, w: any): this;
    rCubicTo(cpx1: any, cpy1: any, cpx2: any, cpy2: any, x: any, y: any): this;
    rMoveTo(x: any, y: any): this;
    rLineTo(x: any, y: any): this;
    rQuadTo(x1: any, y1: any, x2: any, y2: any): this;
    setFillType(fill: any): this;
    setIsVolatile(volatile: any): this;
    stroke(opts: any): any;
    close(): this;
    reset(): this;
    rewind(): this;
    computeTightBounds(): _JsiSkRect.JsiSkRect;
    arcToOval(oval: any, startAngleInDegrees: any, sweepAngleInDegrees: any, forceMoveTo: any): this;
    arcToRotated(rx: any, ry: any, xAxisRotateInDegrees: any, useSmallArc: any, isCCW: any, x: any, y: any): this;
    arcToTangent(x1: any, y1: any, x2: any, y2: any, radius: any): this;
    conicTo(x1: any, y1: any, x2: any, y2: any, w: any): this;
    contains(x: any, y: any): any;
    copy(): JsiSkPath;
    cubicTo(cpx1: any, cpy1: any, cpx2: any, cpy2: any, x: any, y: any): this;
    dash(on: any, off: any, phase: any): any;
    equals(other: any): any;
    getBounds(): _JsiSkRect.JsiSkRect;
    getFillType(): any;
    quadTo(x1: any, y1: any, x2: any, y2: any): this;
    addRect(rect: any, isCCW: any): this;
    addRRect(rrect: any, isCCW: any): this;
    getPoint(index: any): _JsiSkPoint.JsiSkPoint;
    isEmpty(): any;
    isVolatile(): any;
    addCircle(x: any, y: any, r: any): this;
    getLastPt(): _JsiSkPoint.JsiSkPoint;
    op(path: any, op: any): any;
    simplify(): any;
    toSVGString(): any;
    trim(start: any, stop: any, isComplement: any): any;
    transform(m: any): this;
    interpolate(end: any, t: any, output: any): any;
    isInterpolatable(path2: any): any;
    toCmds(): any;
}
import _Host = require("./Host");
import _JsiSkRect = require("./JsiSkRect");
import _JsiSkPoint = require("./JsiSkPoint");
