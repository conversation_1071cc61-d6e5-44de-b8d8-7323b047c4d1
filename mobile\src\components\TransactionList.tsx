import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { formatCurrency, formatTransactionDate, getTransactionTypeColor } from '../lib/utils';
import { useTransactions } from '../hooks/useTransactions';
import type { Transaction, Wallet } from '../lib/types';

interface TransactionListProps {
  transactions: Transaction[];
  wallets: Wallet[];
  onEdit: (transaction: Transaction) => void;
  showHeader?: boolean;
}

interface TransactionItemProps {
  transaction: Transaction;
  wallets: Wallet[];
  onEdit: (transaction: Transaction) => void;
  onDelete: (id: string) => void;
}

function TransactionItem({ transaction, wallets, onEdit, onDelete }: TransactionItemProps) {
  const { categories } = useTransactions();
  
  const wallet = wallets.find(w => w.id === transaction.walletId);
  const toWallet = transaction.toWalletId 
    ? wallets.find(w => w.id === transaction.toWalletId)
    : null;
  
  const category = categories.find(c => c.value === transaction.category);
  const typeColor = getTransactionTypeColor(transaction.type);

  const handleLongPress = () => {
    Alert.alert(
      'Transaction Options',
      'What would you like to do?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => onEdit(transaction) },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Delete Transaction',
              'Are you sure you want to delete this transaction?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => onDelete(transaction.id) },
              ]
            );
          }
        },
      ]
    );
  };

  const getTransactionDescription = () => {
    if (transaction.type === 'transfer') {
      return `${wallet?.name} → ${toWallet?.name}`;
    }
    return transaction.description || category?.label || 'Transaction';
  };

  const getTransactionIcon = () => {
    if (transaction.type === 'transfer') {
      return 'swap-horiz';
    }
    return transaction.type === 'income' ? 'trending-up' : 'trending-down';
  };

  return (
    <TouchableOpacity
      style={styles.transactionItem}
      onPress={() => onEdit(transaction)}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
    >
      <View style={styles.transactionLeft}>
        <View style={[styles.iconContainer, { backgroundColor: `${typeColor}20` }]}>
          <MaterialIcons 
            name={getTransactionIcon()} 
            size={20} 
            color={typeColor} 
          />
        </View>
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionDescription} numberOfLines={1}>
            {getTransactionDescription()}
          </Text>
          <View style={styles.transactionMeta}>
            <Text style={styles.transactionDate}>
              {formatTransactionDate(transaction.date)}
            </Text>
            {transaction.type !== 'transfer' && category && (
              <>
                <Text style={styles.metaSeparator}>•</Text>
                <Text style={styles.transactionCategory}>{category.label}</Text>
              </>
            )}
          </View>
        </View>
      </View>
      <View style={styles.transactionRight}>
        <Text style={[styles.transactionAmount, { color: typeColor }]}>
          {transaction.type === 'expense' ? '-' : '+'}
          {formatCurrency(transaction.amount)}
        </Text>
        <Text style={styles.walletName} numberOfLines={1}>
          {wallet?.name}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

export default function TransactionList({
  transactions,
  wallets,
  onEdit,
  showHeader = true,
}: TransactionListProps) {
  const { deleteTransaction } = useTransactions();

  const handleDelete = async (id: string) => {
    await deleteTransaction(id);
  };

  const renderItem = ({ item }: { item: Transaction }) => (
    <TransactionItem
      transaction={item}
      wallets={wallets}
      onEdit={onEdit}
      onDelete={handleDelete}
    />
  );

  const renderHeader = () => {
    if (!showHeader) return null;
    
    return (
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Transactions</Text>
        <Text style={styles.headerSubtitle}>
          {transactions.length} transaction{transactions.length !== 1 ? 's' : ''}
        </Text>
      </View>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="receipt" size={48} color="#ccc" />
      <Text style={styles.emptyText}>No transactions found</Text>
      <Text style={styles.emptySubtext}>Add a transaction to get started</Text>
    </View>
  );

  return (
    <FlatList
      data={transactions}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      ListHeaderComponent={renderHeader}
      ListEmptyComponent={renderEmpty}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={transactions.length === 0 ? styles.emptyList : undefined}
    />
  );
}

const styles = StyleSheet.create({
  header: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  transactionLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionDate: {
    fontSize: 12,
    color: '#666',
  },
  metaSeparator: {
    fontSize: 12,
    color: '#666',
    marginHorizontal: 6,
  },
  transactionCategory: {
    fontSize: 12,
    color: '#666',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  walletName: {
    fontSize: 12,
    color: '#666',
    maxWidth: 80,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  emptyList: {
    flexGrow: 1,
    justifyContent: 'center',
  },
});
