# Migration from Local Storage to Supabase

## Overview
Your ExpenseFlow app has been updated to store data in Supabase instead of local storage. This provides:
- ✅ Data persistence across devices
- ✅ Real-time sync when signed in
- ✅ Automatic backups
- ✅ User-specific data isolation

## Setup Instructions

### 1. Create the Database Table
Run the SQL script in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `supabase-migration.sql`
4. Click **Run** to execute the script

This will create:
- `profiles` table with proper RLS policies
- Automatic profile creation on user signup
- Timestamp tracking for data updates

### 2. Configure Google OAuth (Optional)
If you want to use Google authentication:

1. Go to **Authentication > Providers** in Supabase
2. Enable **Google** provider
3. Add your Google OAuth credentials
4. Set redirect URL to: `http://localhost:3000/auth/callback` (for development)

### 3. Test the Migration

1. **Clear existing local storage** (optional):
   ```javascript
   // Run in browser console to clear old data
   localStorage.clear();
   ```

2. **Sign in to your app** - Data will now be stored in Supabase

3. **Verify data persistence**:
   - Add some transactions/wallets
   - Sign out and sign back in
   - Data should persist

## Data Structure

The `profiles.data` JSON field contains:
```json
{
  "wallets": [...],
  "transactions": [...],
  "budgets": [...],
  "categories": [...],
  "subscription": {"tier": "free"}
}
```

## Key Changes Made

### 1. Updated `use-transactions.ts`
- Replaced localStorage functions with Supabase operations
- Added authentication-aware data loading
- Automatic data sync on changes

### 2. Added Authentication Integration
- Data is now user-specific (tied to auth.users.id)
- Automatic initialization when user signs in
- Graceful handling when user is not authenticated

### 3. Enhanced Error Handling
- Toast notifications for sync errors
- Fallback to default data if Supabase is unavailable
- Proper loading states

## Troubleshooting

### Data Not Saving
- Check browser console for Supabase errors
- Verify RLS policies are correctly set
- Ensure user is properly authenticated

### Authentication Issues
- Check Supabase auth configuration
- Verify redirect URLs are correct
- Check browser network tab for auth errors

### Performance
- Data is cached locally and synced to Supabase
- Initial load may be slower as data is fetched from database
- Subsequent operations are optimized with local state

## Rollback (if needed)
If you need to rollback to local storage:
1. Restore the original `use-transactions.ts` from git history
2. Remove the AuthGuard from `src/app/page.tsx`
3. Clear any Supabase-related imports
