
"use client";

import * as React from "react";
import { format } from "date-fns";
import { ArrowDownLeft, ArrowUpRight, History, TrendingDown, TrendingUp, ArrowRightLeft, MoreVertical, Edit, Trash2 } from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { Transaction, Wallet } from "@/lib/types";
import { formatCurrency, cn } from "@/lib/utils";
import { useTransactions } from "@/hooks/use-transactions";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, <PERSON><PERSON><PERSON><PERSON>ogHead<PERSON>, AlertDialogTitle } from "../ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";

interface TransactionListProps {
  transactions: Transaction[];
  wallets: Wallet[];
  totalIncome: number;
  totalExpense: number;
  onEdit: (transaction: Transaction) => void;
}

export function TransactionList({ transactions, wallets, totalIncome, totalExpense, onEdit }: TransactionListProps) {
  const { categories, deleteTransaction } = useTransactions();
  const { toast } = useToast();
  const [transactionToDelete, setTransactionToDelete] = React.useState<Transaction | null>(null);
  const isMobile = useIsMobile();

  const sortedTransactions = React.useMemo(() => {
    return [...transactions].sort((a, b) => b.date.getTime() - a.date.getTime());
  }, [transactions]);

  const handleDeleteClick = (transaction: Transaction) => {
    setTransactionToDelete(transaction);
  };
  
  const confirmDelete = () => {
    if (transactionToDelete) {
      deleteTransaction(transactionToDelete.id);
      toast({
        title: "Transaction Deleted",
        description: "The transaction has been successfully deleted.",
      });
      setTransactionToDelete(null);
    }
  };

  const listContent = (
    <div className="p-1 sm:p-4 space-y-4">
    {sortedTransactions.map((transaction) => {
      const category = categories.find(
        (c) => c.value === transaction.category
      );
      const fromWallet = wallets.find((w) => w.id === transaction.walletId);
      const Icon = category?.icon;

      const renderTransactionDetails = () => {
        if (transaction.type === 'transfer') {
            const toWallet = wallets.find(w => w.id === transaction.toWalletId);
            return (
                 <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
                    <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-primary/10 sm:h-12 sm:w-12">
                        <ArrowRightLeft className="h-5 w-5 sm:h-6 sm:w-6" />
                    </div>
                    <div className="flex-1 min-w-0">
                        <p className="font-semibold truncate">Transfer</p>
                        <div className="flex flex-wrap items-center gap-x-2 text-xs text-muted-foreground sm:text-sm">
                            <span className="truncate">{fromWallet?.name}</span>
                            <ArrowRightLeft className="h-3 w-3" />
                            <span className="truncate">{toWallet?.name}</span>
                        </div>
                    </div>
                    <div className="font-semibold text-base flex shrink-0 items-center gap-1 sm:text-lg">
                        {formatCurrency(transaction.amount)}
                    </div>
                </div>
            );
        }

        return (
            <div className={`flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0 ${isMobile && 'px-4'}`}>
                <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-primary/10 sm:h-12 sm:w-12">
                    {Icon && <Icon className="h-5 w-5 sm:h-6 sm:w-6" />}
                </div>
                <div className="flex-1 min-w-0">
                  {!isMobile &&
                    <p className="font-semibold truncate">{transaction.description}</p>}
                    <div className="flex flex-wrap items-center gap-x-2 text-xs text-muted-foreground sm:text-sm">
                        {!isMobile &&  <Badge variant="outline" className="capitalize">{category?.label}</Badge>}
                        {!isMobile &&  <span className="hidden sm:inline">&bull;</span>}
                        {!isMobile &&  <span className="truncate">{fromWallet?.name}</span>}
                        {!isMobile &&  <span className="hidden sm:inline">&bull;</span>}
                        <span>{format(transaction.date, "MMM d, yyyy")}</span>
                    </div>
                </div>
                <div
                    className={cn(
                    "font-semibold text-base flex shrink-0 items-center gap-1 sm:text-lg",
                    transaction.type === "income"
                        ? "text-green-400"
                        : "text-red-400"
                    )}
                >
                    {transaction.type === "income" ? (
                    <ArrowUpRight className="h-4 w-4 sm:h-5 sm:w-5" />
                    ) : (
                    <ArrowDownLeft className="h-4 w-4 sm:h-5 sm:w-5" />
                    )}
                    {formatCurrency(transaction.amount)}
                </div>
            </div>
        )
      }

      return (
        <div key={transaction.id} className="flex items-center">
            {renderTransactionDetails()}
            <div className="ml-2">
                 <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEdit(transaction)}>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Edit</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(transaction)}
                        className="text-red-400"
                      >
                        <Trash2 className="mr-2 h-4 w-4 text-red-400" />
                        <span>Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
            </div>
        </div>
      );
    })}
    </div>
  );


  return (
    <>
        <Card className="bg-card-gradient h-full w-full flex flex-col">
          <CardHeader>
            <CardTitle>This Month's Transactions</CardTitle>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-sm mt-2">
                <div className="flex items-center gap-2 font-medium leading-none">
                    <TrendingUp className="h-4 w-4 text-green-400" /> 
                    <span className="text-muted-foreground">Income:</span> 
                    <span>{formatCurrency(totalIncome)}</span>
                </div>
                <div className="flex items-center gap-2 font-medium leading-none">
                    <TrendingDown className="h-4 w-4 text-red-400" /> 
                    <span className="text-muted-foreground">Expense:</span>
                    <span>{formatCurrency(totalExpense)}</span>
                </div>
            </div>
          </CardHeader>
          <CardContent className="p-0 flex-1">
            <ScrollArea className="h-[calc(100vh-320px)] w-full">
                  {listContent}
            </ScrollArea>
          </CardContent>
        </Card>
        <AlertDialog open={!!transactionToDelete} onOpenChange={(open) => !open && setTransactionToDelete(null)}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete this transaction.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={confirmDelete} className="bg-destructive hover:bg-destructive/90">
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    </>
  );
}
