export const __esModule: boolean;
export class SkiaSGRoot {
    constructor(Skia: any, nativeId?: number);
    Skia: any;
    container: {
        redraw(): void;
        mapperId: any;
        Skia: any;
        nativeId: any;
        root: any;
        _root: any;
        mount(): void;
        unmounted: boolean | undefined;
        unmount(): void;
        drawOnCanvas(canvas: any): void;
    } | {
        redraw(): void;
        recording: {
            commands: any;
            paintPool: never[];
        } | undefined;
        mapperId: any;
        Skia: any;
        nativeId: any;
        root: any;
        _root: any;
        mount(): void;
        unmounted: boolean | undefined;
        unmount(): void;
        drawOnCanvas(canvas: any): void;
    } | {
        redraw(): void;
        recording: {
            commands: any;
            paintPool: never[];
            animationValues: any;
        } | undefined;
        Skia: any;
        nativeId: any;
        root: any;
        _root: any;
        mount(): void;
        unmounted: boolean | undefined;
        unmount(): void;
        drawOnCanvas(canvas: any): void;
    };
    root: any;
    get sg(): {
        type: any;
        props: {};
        children: any;
        isDeclaration: boolean;
    };
    updateContainer(element: any): Promise<any>;
    render(element: any): Promise<void>;
    drawOnCanvas(canvas: any): void;
    getPicture(): any;
    unmount(): Promise<any>;
}
