export const __esModule: boolean;
export class JsiSkPathEffectFactory extends _Host.Host {
    MakeCorner(radius: any): _JsiSkPathEffect.JsiSkPathEffect | null;
    MakeDash(intervals: any, phase: any): _JsiSkPathEffect.JsiSkPathEffect;
    MakeDiscrete(segLength: any, dev: any, seedAssist: any): _JsiSkPathEffect.JsiSkPathEffect;
    MakeCompose(_outer: any, _inner: any): jest.Mock<any, any, any>;
    MakeSum(_outer: any, _inner: any): jest.Mock<any, any, any>;
    MakeLine2D(width: any, matrix: any): _JsiSkPathEffect.JsiSkPathEffect | null;
    MakePath1D(path: any, advance: any, phase: any, style: any): _JsiSkPathEffect.JsiSkPathEffect | null;
    MakePath2D(matrix: any, path: any): _JsiSkPathEffect.JsiSkPathEffect | null;
}
import _Host = require("./Host");
import _JsiSkPathEffect = require("./JsiSkPathEffect");
