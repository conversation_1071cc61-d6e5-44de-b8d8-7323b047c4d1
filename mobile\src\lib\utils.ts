import { format } from 'date-fns';

/**
 * Format currency amount
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Format date for display
 */
export function formatDate(date: Date): string {
  return format(date, 'MMM dd, yyyy');
}

/**
 * Format date for transaction list
 */
export function formatTransactionDate(date: Date): string {
  return format(date, 'MMM dd');
}

/**
 * Generate unique ID
 */
export function generateId(): string {
  return Date.now().toString();
}

/**
 * Clamp a number between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Calculate percentage
 */
export function calculatePercentage(value: number, total: number): number {
  if (total === 0) return 0;
  return (value / total) * 100;
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Get transaction type color
 */
export function getTransactionTypeColor(type: 'income' | 'expense' | 'transfer'): string {
  switch (type) {
    case 'income':
      return '#4CAF50'; // Green
    case 'expense':
      return '#F44336'; // Red
    case 'transfer':
      return '#2196F3'; // Blue
    default:
      return '#757575'; // Gray
  }
}

/**
 * Get transaction type icon
 */
export function getTransactionTypeIcon(type: 'income' | 'expense' | 'transfer'): string {
  switch (type) {
    case 'income':
      return 'trending-up';
    case 'expense':
      return 'trending-down';
    case 'transfer':
      return 'swap-horiz';
    default:
      return 'help';
  }
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Get initials from name
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
}
