class KotlinExpoModulesCorePlugin implements Plugin<Project> {
  void apply(Project project) {
    // For compatibility reasons the plugin needs to declare that it provides common build.gradle
    // options for the modules
    project.rootProject.ext.expoProvidesDefaultConfig = {
      true
    }

    project.ext.safeExtGet = { prop, fallback ->
      project.rootProject.ext.has(prop) ? project.rootProject.ext.get(prop) : fallback
    }

    project.buildscript {
      project.ext.kotlinVersion = {
        project.rootProject.ext.has("kotlinVersion")
            ? project.rootProject.ext.get("kotlinVersion")
            : "2.0.21"
      }

      project.ext.kspVersion = {
        def kspVersionsMap = [
          "1.6.10": "1.6.10-1.0.4",
          "1.6.21": "1.6.21-1.0.6",
          "1.7.22": "1.7.22-1.0.8",
          "1.8.0": "1.8.0-1.0.9",
          "1.8.10": "1.8.10-1.0.9",
          "1.8.20": "1.8.20-1.0.11",
          "1.8.22": "1.8.22-1.0.11",
          "1.9.23": "1.9.23-1.0.20",
          "1.9.24": "1.9.24-1.0.20",
          "2.0.21": "2.0.21-1.0.28"
        ]

        project.rootProject.ext.has("kspVersion")
          ? project.rootProject.ext.get("kspVersion")
          : kspVersionsMap.containsKey(project.ext.kotlinVersion())
          ? kspVersionsMap.get(project.ext.kotlinVersion())
          : "2.0.21-1.0.28"
      }
    }
  }
}

ext.applyKotlinExpoModulesCorePlugin = {
  try {
    // Tries to apply the kotlin-android plugin if the client project does not apply yet.
    // On previous `applyKotlinExpoModulesCorePlugin`, it is inside the `project.buildscript` block.
    // We cannot use `project.plugins.hasPlugin()` yet but only to try-catch instead.
    apply plugin: 'kotlin-android'
  } catch (e) {}

  apply plugin: KotlinExpoModulesCorePlugin
}

// Apply JVM Toolchain version for KSP
ext.applyKspJvmToolchain = {
  project.ksp {
    kotlin.jvmToolchain(17)
  }
}

// Setup build options that are common for all modules
ext.useDefaultAndroidSdkVersions = {
  project.android {
    compileSdkVersion project.ext.safeExtGet("compileSdkVersion", 34)

    defaultConfig {
      minSdkVersion project.ext.safeExtGet("minSdkVersion", 23)
      targetSdkVersion project.ext.safeExtGet("targetSdkVersion", 34)
    }

    lintOptions {
      abortOnError false
    }
  }
}

ext.useExpoPublishing = {
  if (!project.plugins.hasPlugin('maven-publish')) {
    apply plugin: 'maven-publish'
  }

  project.android {
    publishing {
      singleVariant("release") {
        withSourcesJar()
      }
    }
  }

  project.afterEvaluate {
    publishing {
      publications {
        release(MavenPublication) {
          from components.release
        }
      }
      repositories {
        maven {
          url = mavenLocal().url
        }
      }
    }
  }
}

ext.useCoreDependencies = {
  dependencies {
    // Avoids cyclic dependencies
    if (!project.project.name.startsWith("expo-modules-core")) {
      implementation project.project(':expo-modules-core')
    }
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${project.ext.kotlinVersion()}"
  }
}

ext.boolish = { value ->
  return value.toString().toBoolean()
}
