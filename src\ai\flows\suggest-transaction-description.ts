'use server';

/**
 * @fileOverview This file defines a Genkit flow for suggesting transaction descriptions using AI.
 *
 * - suggestTransactionDescription - A function that generates transaction descriptions based on the provided context.
 * - SuggestTransactionDescriptionInput - The input type for the suggestTransactionDescription function.
 * - SuggestTransactionDescriptionOutput - The return type for the suggestTransactionDescription function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SuggestTransactionDescriptionInputSchema = z.object({
  transactionType: z.enum(['expense', 'income']).describe('The type of transaction (expense or income).'),
  category: z.string().describe('The category of the transaction (e.g., groceries, salary).'),
  amount: z.number().describe('The amount of the transaction.'),
  wallet: z.string().describe('The wallet used for the transaction.'),
  existingDescription: z.string().optional().describe('The existing description of the transaction, if any.'),
});
export type SuggestTransactionDescriptionInput = z.infer<typeof SuggestTransactionDescriptionInputSchema>;

const SuggestTransactionDescriptionOutputSchema = z.object({
  suggestedDescription: z.string().describe('The AI-suggested description for the transaction.'),
});
export type SuggestTransactionDescriptionOutput = z.infer<typeof SuggestTransactionDescriptionOutputSchema>;

export async function suggestTransactionDescription(
  input: SuggestTransactionDescriptionInput
): Promise<SuggestTransactionDescriptionOutput> {
  return suggestTransactionDescriptionFlow(input);
}

const prompt = ai.definePrompt({
  name: 'suggestTransactionDescriptionPrompt',
  input: {schema: SuggestTransactionDescriptionInputSchema},
  output: {schema: SuggestTransactionDescriptionOutputSchema},
  prompt: `You are an AI assistant that suggests descriptions for financial transactions.

  Given the following information about a transaction, suggest a concise and informative description.
  The description should be no more than 20 words.

  Transaction Type: {{{transactionType}}}
  Category: {{{category}}}
  Amount: {{{amount}}}
  Wallet: {{{wallet}}}
  Existing Description: {{{existingDescription}}}

  Suggest a description for this transaction:
`,
});

const suggestTransactionDescriptionFlow = ai.defineFlow(
  {
    name: 'suggestTransactionDescriptionFlow',
    inputSchema: SuggestTransactionDescriptionInputSchema,
    outputSchema: SuggestTransactionDescriptionOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
