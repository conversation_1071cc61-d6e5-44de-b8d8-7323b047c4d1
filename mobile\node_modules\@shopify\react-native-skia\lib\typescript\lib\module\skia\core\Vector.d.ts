export function vec(x: number | undefined, y: any): import("../../../..").SkPoint;
export function point(x: number | undefined, y: any): import("../../../..").SkPoint;
export function neg(a: any): import("../../../..").SkPoint;
export function add(a: any, b: any): import("../../../..").SkPoint;
export function sub(a: any, b: any): import("../../../..").SkPoint;
export function dist(a: any, b: any): number;
