"use client";

import * as React from "react";
import { initialWallets, initialTransactions, initialBudgets, initialCategories } from "@/lib/constants";
import type { Wallet, Transaction, Budget, Category, CategoryInfo, Subscription } from "@/lib/types";
import { NeonIcon } from "@/components/icons";
import { ICONS } from "@/lib/constants";
import { subMonths } from "date-fns";
import { toast } from "./use-toast";
import { createClient } from "@/utils/supabase/client";
import { useAuth } from "./use-auth";

const FREE_TIER_LIMITS = {
  wallets: 3,
  categories: 5,
  historyMonths: 2,
};

type UserData = {
  wallets: Wallet[];
  transactions: Transaction[];
  budgets: Budget[];
  categories: CategoryInfo[];
  subscription: Subscription;
};

// Supabase data management functions
async function loadFromSupabase(userId: string): Promise<UserData> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('data')
      .eq('id', userId)
      .single();

    if (error) {
      // If profile doesn't exist, create it with default data
      if (error.code === 'PGRST116') {
        console.log('Profile not found, creating new profile for user:', userId);
        const defaultData = getDefaultData();
        await saveToSupabase(userId, defaultData);
        return defaultData;
      }
      console.warn('Error loading from Supabase:', error);
      return getDefaultData();
    }

    if (data?.data) {
      // Parse the JSON data and re-hydrate dates for transactions
      const userData = data.data as UserData;
      if (userData.transactions) {
        userData.transactions = userData.transactions.map(t => ({
          ...t,
          date: new Date(t.date)
        }));
      }
      return {
        wallets: userData.wallets || initialWallets,
        transactions: userData.transactions || initialTransactions,
        budgets: userData.budgets || initialBudgets,
        categories: userData.categories || initialCategories,
        subscription: userData.subscription || { tier: 'free' },
      };
    }
  } catch (error) {
    console.warn('Error parsing Supabase data:', error);
  }

  return getDefaultData();
}

async function saveToSupabase(userId: string, data: UserData) {
  const supabase = createClient();
  
  try {
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        data: data
      });

    if (error) {
      console.warn('Error saving to Supabase:', error);
      toast({
        variant: "destructive",
        title: "Sync Error",
        description: "Failed to save data to cloud. Your changes are saved locally.",
      });
    }
  } catch (error) {
    console.warn('Error saving to Supabase:', error);
  }
}

function getDefaultData(): UserData {
  return {
    wallets: initialWallets,
    transactions: initialTransactions,
    budgets: initialBudgets,
    categories: initialCategories,
    subscription: { tier: 'free' },
  };
}

let walletsStore: Wallet[] = initialWallets;
let transactionsStore: Transaction[] = initialTransactions;
let budgetsStore: Budget[] = initialBudgets;
let categoriesStore: Category[] = initialCategories.map(c => ({
    ...c,
    icon: NeonIcon(ICONS.find(i => i.name === c.iconName)?.icon || ICONS[0].icon)
}));
let subscriptionStore: Subscription = { tier: 'free' };

const listeners = new Set<() => void>();

async function initializeStores(userId: string) {
  const userData = await loadFromSupabase(userId);
  
  walletsStore = userData.wallets;
  transactionsStore = userData.transactions;
  budgetsStore = userData.budgets;
  subscriptionStore = userData.subscription;
  categoriesStore = userData.categories.map(c => ({
    ...c,
    icon: NeonIcon(ICONS.find(i => {
      return i.name === c.iconName
    })?.icon || ICONS[0].icon)
  }));

  // Enforce free tier data retention
  if (subscriptionStore.tier === 'free') {
    const twoMonthsAgo = subMonths(new Date(), FREE_TIER_LIMITS.historyMonths);
    const originalCount = transactionsStore.length;
    transactionsStore = transactionsStore.filter(t => t.date >= twoMonthsAgo);
    if (transactionsStore.length < originalCount) {
      await saveAllData(userId);
    }
  }
}

const broadcastChanges = () => {
  listeners.forEach((listener) => listener());
};

async function saveAllData(userId: string) {
  const userData: UserData = {
    wallets: walletsStore,
    transactions: transactionsStore,
    budgets: budgetsStore,
    categories: categoriesStore.map(c => {
      console.log(c.icon.displayName);
      return {
      value: c.value,
      label: c.label,
      iconName: c.icon.displayName?.replace("Neon(", "").replace(")", "") || ICONS[0].name,
      type: c.type
    }}),
    subscription: subscriptionStore,
  };
  
  await saveToSupabase(userId, userData);
}

const updateWalletBalance = (walletId: string, amount: number, type: 'add' | 'subtract') => {
    walletsStore = walletsStore.map(wallet => {
        if (wallet.id === walletId) {
            const newBalance = type === 'add' ? wallet.balance + amount : wallet.balance - amount;
            return { ...wallet, balance: newBalance };
        }
        return wallet;
    });
};

const createTransactionApi = (userId: string) => ({
  getWallets: () => walletsStore,
  getTransactions: () => transactionsStore,
  getBudgets: () => budgetsStore,
  getCategories: () => categoriesStore.map(c => ({
    ...c,
    iconName: ICONS.find(i => i.icon === c.icon)?.name || ICONS[0].name
  })),
  getSubscription: () => subscriptionStore,

  addWallet: async (data: Omit<Wallet, 'id' | 'balance' | 'currency'> & { initialBalance: number }) => {
    if (subscriptionStore.tier === 'free' && walletsStore.length >= FREE_TIER_LIMITS.wallets) {
      toast({
        variant: "destructive",
        title: "Upgrade to Premium",
        description: "You've reached the maximum number of wallets for the free tier.",
      });
      return;
    }
    const newWallet: Wallet = {
      id: `wallet-${Date.now()}`,
      name: data.name,
      balance: data.initialBalance,
      icon: data.icon,
      currency: "USD",
    };
    walletsStore = [...walletsStore, newWallet];
    await saveAllData(userId);
    broadcastChanges();
  },

  editWallet: async (data: Omit<Wallet, 'id' | 'balance' | 'currency'> & { initialBalance: number }, id: string) => {
    walletsStore = walletsStore.map(wallet =>
      wallet.id === id
        ? { ...wallet, name: data.name, icon: data.icon }
        : wallet
    );
    await saveAllData(userId);
    broadcastChanges();
  },

  deleteWallet: async (id: string) => {
    walletsStore = walletsStore.filter(wallet => wallet.id !== id);
    transactionsStore = transactionsStore.filter(tx => tx.walletId !== id && tx.toWalletId !== id);
    await saveAllData(userId);
    broadcastChanges();
  },

  addTransaction: async (newTransaction: Omit<Transaction, 'id' | 'date'>) => {
    transactionsStore = [
      { ...newTransaction, id: Date.now().toString(), date: new Date() },
      ...transactionsStore,
    ];

    if (newTransaction.type === 'transfer') {
        updateWalletBalance(newTransaction.walletId, newTransaction.amount, 'subtract');
        if (newTransaction.toWalletId) {
            updateWalletBalance(newTransaction.toWalletId, newTransaction.amount, 'add');
        }
    } else {
        const balanceUpdateType = newTransaction.type === 'income' ? 'add' : 'subtract';
        updateWalletBalance(newTransaction.walletId, newTransaction.amount, balanceUpdateType);
    }

    await saveAllData(userId);
    broadcastChanges();
  },

  editTransaction: async (updatedTransaction: Transaction) => {
    const originalTransaction = transactionsStore.find(t => t.id === updatedTransaction.id);
    if (!originalTransaction) return;

    // Revert old transaction's effect on wallet balance
    if (originalTransaction.type === 'transfer') {
        updateWalletBalance(originalTransaction.walletId, originalTransaction.amount, 'add');
        if (originalTransaction.toWalletId) {
             updateWalletBalance(originalTransaction.toWalletId, originalTransaction.amount, 'subtract');
        }
    } else {
        const balanceUpdateType = originalTransaction.type === 'income' ? 'subtract' : 'add';
        updateWalletBalance(originalTransaction.walletId, originalTransaction.amount, balanceUpdateType);
    }

    // Apply new transaction's effect
    if (updatedTransaction.type === 'transfer') {
        updateWalletBalance(updatedTransaction.walletId, updatedTransaction.amount, 'subtract');
        if (updatedTransaction.toWalletId) {
            updateWalletBalance(updatedTransaction.toWalletId, updatedTransaction.amount, 'add');
        }
    } else {
        const balanceUpdateType = updatedTransaction.type === 'income' ? 'add' : 'subtract';
        updateWalletBalance(updatedTransaction.walletId, updatedTransaction.amount, balanceUpdateType);
    }

    transactionsStore = transactionsStore.map(t => t.id === updatedTransaction.id ? updatedTransaction : t);
    await saveAllData(userId);
    broadcastChanges();
  },

  deleteTransaction: async (id: string) => {
    const transactionToDelete = transactionsStore.find(t => t.id === id);
    if (!transactionToDelete) return;

    // Revert transaction's effect on wallet balance
    if (transactionToDelete.type === 'transfer') {
        updateWalletBalance(transactionToDelete.walletId, transactionToDelete.amount, 'add');
        if (transactionToDelete.toWalletId) {
             updateWalletBalance(transactionToDelete.toWalletId, transactionToDelete.amount, 'subtract');
        }
    } else {
        const balanceUpdateType = transactionToDelete.type === 'income' ? 'subtract' : 'add';
        updateWalletBalance(transactionToDelete.walletId, transactionToDelete.amount, balanceUpdateType);
    }

    transactionsStore = transactionsStore.filter(t => t.id !== id);
    await saveAllData(userId);
    broadcastChanges();
  },

  setBudgets: async (newBudgets: Budget[]) => {
    budgetsStore = newBudgets;
    await saveAllData(userId);
    broadcastChanges();
  },

  addBudget: async (data: { category: string; amount: number }) => {
    const newBudget: Budget = {
      id: `budget-${data.category}`,
      category: data.category,
      amount: data.amount,
    };
    budgetsStore = [...budgetsStore, newBudget];
    await saveAllData(userId);
    broadcastChanges();
  },

  removeBudget: async (categoryValue: string) => {
    budgetsStore = budgetsStore.filter(b => b.category !== categoryValue);
    await saveAllData(userId);
    broadcastChanges();
  },

  addCategory: async (data: Omit<Category, 'icon'> & { iconName: string }) => {
    if (subscriptionStore.tier === 'free' && categoriesStore.length >= FREE_TIER_LIMITS.categories) {
       toast({
        variant: "destructive",
        title: "Upgrade to Premium",
        description: "You've reached the maximum number of categories for the free tier.",
      });
      return;
    }

    console.log(data.iconName);

    const newCategory: Category = {
      value: data.value,
      label: data.label,
      icon: NeonIcon(ICONS.find(i => i.name === data.iconName)?.icon || ICONS[0].icon),
      type: data.type
    };

    categoriesStore = [...categoriesStore, newCategory];
    await saveAllData(userId);
    broadcastChanges();
  },

  editCategory: async (data: Omit<Category, 'icon'> & { iconName: string }) => {
    categoriesStore = categoriesStore.map(c =>
      c.value === data.value ? {
        value: data.value,
        label: data.label,
        icon: NeonIcon(ICONS.find(i => i.name === data.iconName)?.icon || ICONS[0].icon),
        type: data.type
      } : c
    );

    await saveAllData(userId);
    broadcastChanges();
  },

  deleteCategory: async (value: string) => {
    categoriesStore = categoriesStore.filter(c => c.value !== value);
    await saveAllData(userId);
    broadcastChanges();
  },

  setSubscriptionTier: async (tier: 'free' | 'premium') => {
    subscriptionStore = { tier };
    await saveAllData(userId);
    broadcastChanges();
  },

  subscribe: (callback: () => void) => {
    listeners.add(callback);
    return () => listeners.delete(callback);
  },
});

export function useTransactions() {
  const { user, isSignedIn } = useAuth();
  const [data, setData] = React.useState({
    wallets: initialWallets,
    transactions: initialTransactions,
    budgets: initialBudgets,
    categories: initialCategories,
    subscription: { tier: 'free' as 'free' | 'premium' },
  });

  const [isInitialized, setIsInitialized] = React.useState(false);
  const [transactionApi, setTransactionApi] = React.useState<ReturnType<typeof createTransactionApi> | null>(null);

  React.useEffect(() => {
    if (!isSignedIn || !user) {
      setIsInitialized(false);
      setTransactionApi(null);
      return;
    }

    const initializeData = async () => {
      await initializeStores(user.id);
      const api = createTransactionApi(user.id);
      setTransactionApi(api);

      setData({
        wallets: api.getWallets(),
        transactions: api.getTransactions(),
        budgets: api.getBudgets(),
        categories: api.getCategories(),
        subscription: api.getSubscription(),
      });
      setIsInitialized(true);

      const unsubscribe = api.subscribe(() => {
        setData({
          wallets: api.getWallets(),
          transactions: api.getTransactions(),
          budgets: api.getBudgets(),
          categories: api.getCategories(),
          subscription: api.getSubscription(),
        });
      });

      return unsubscribe;
    };

    initializeData();
  }, [isSignedIn, user]);

  return {
    isInitialized,
    wallets: data.wallets,
    transactions: data.transactions,
    budgets: data.budgets,
    categories: data.categories,
    subscription: data.subscription,
    addWallet: transactionApi?.addWallet || (() => {}),
    editWallet: transactionApi?.editWallet || (() => {}),
    deleteWallet: transactionApi?.deleteWallet || (() => {}),
    addTransaction: transactionApi?.addTransaction || (() => {}),
    editTransaction: transactionApi?.editTransaction || (() => {}),
    deleteTransaction: transactionApi?.deleteTransaction || (() => {}),
    setBudgets: transactionApi?.setBudgets || (() => {}),
    addBudget: transactionApi?.addBudget || (() => {}),
    removeBudget: transactionApi?.removeBudget || (() => {}),
    addCategory: transactionApi?.addCategory || (() => {}),
    editCategory: transactionApi?.editCategory || (() => {}),
    deleteCategory: transactionApi?.deleteCategory || (() => {}),
    setSubscriptionTier: transactionApi?.setSubscriptionTier || (() => {})
  };
}
