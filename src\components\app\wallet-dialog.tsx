"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus, Save, Check, ChevronsUpDown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { WALLET_ICONS } from "@/lib/constants";
import type { Wallet } from "@/lib/types";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";

const walletSchema = z.object({
  name: z.string().min(1, "Wallet name is required."),
  initialBalance: z.coerce.number().min(0, "Balance must be positive."),
  icon: z.string().min(1, "Icon is required."),
});

type WalletFormValues = z.infer<typeof walletSchema>;

type WalletSaveData = Omit<Wallet, 'id' | 'balance' | 'currency'> & { initialBalance: number };

interface WalletDialogProps {
  onSave: (data: WalletSaveData, id?: string) => void;
  walletToEdit?: Wallet;
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function WalletDialog({ onSave, walletToEdit, children, open, onOpenChange }: WalletDialogProps) {
  const form = useForm<WalletFormValues>({
    resolver: zodResolver(walletSchema),
    defaultValues: {
      name: "",
      initialBalance: 0,
      icon: WALLET_ICONS[0].name,
    },
  });

  React.useEffect(() => {
    if (walletToEdit && open) {
      form.reset({
        name: walletToEdit.name,
        initialBalance: walletToEdit.balance,
        icon: walletToEdit.icon,
      });
    } else if (!walletToEdit && open) {
      form.reset({
        name: "",
        initialBalance: 0,
        icon: WALLET_ICONS[0].name,
      });
    }
  }, [walletToEdit, open, form]);

  const onSubmit = (data: WalletFormValues) => {
    onSave(data, walletToEdit?.id);
    onOpenChange(false);
  };
  
  const selectedIcon = WALLET_ICONS.find(i => i.name === form.watch('icon'));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-card-gradient">
        <DialogHeader>
          <DialogTitle>{walletToEdit ? "Edit Wallet" : "Add New Wallet"}</DialogTitle>
          <DialogDescription>
            {walletToEdit ? "Update your wallet's details." : "Create a new wallet to manage your finances."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Wallet Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Savings" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="initialBalance"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{walletToEdit ? "Balance" : "Initial Balance"}</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="0.00" {...field} disabled={!!walletToEdit} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Icon</FormLabel>
                    <Popover>
                        <PopoverTrigger asChild>
                            <FormControl>
                                <Button
                                    variant="outline"
                                    role="combobox"
                                    className={cn(
                                        "w-full justify-between",
                                        !field.value && "text-muted-foreground"
                                    )}
                                >
                                    {selectedIcon ? (
                                        <div className="flex items-center gap-2">
                                            <selectedIcon.icon className="h-4 w-4" />
                                            {selectedIcon.name}
                                        </div>
                                    ) : (
                                        "Select icon"
                                    )}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                            <Command>
                                <CommandInput placeholder="Search icons..." />
                                <CommandList>
                                  <CommandEmpty>No icon found.</CommandEmpty>
                                  <CommandGroup>
                                      {WALLET_ICONS.map((icon) => (
                                          <CommandItem
                                              value={icon.name}
                                              key={icon.name}
                                              onSelect={() => {
                                                  form.setValue("icon", icon.name);
                                              }}
                                          >
                                              <Check
                                                  className={cn(
                                                      "mr-2 h-4 w-4",
                                                      field.value === icon.name ? "opacity-100" : "opacity-0"
                                                  )}
                                              />
                                              <icon.icon className="mr-2 h-4 w-4" />
                                              {icon.name}
                                          </CommandItem>
                                      ))}
                                  </CommandGroup>
                                </CommandList>
                            </Command>
                        </PopoverContent>
                    </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">
                {walletToEdit ? <Save className="mr-2 h-4 w-4" /> : <Plus className="mr-2 h-4 w-4" />}
                {walletToEdit ? "Save Changes" : "Add Wallet"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
