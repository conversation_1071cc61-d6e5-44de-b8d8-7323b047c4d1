import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { useTransactions } from '../hooks/useTransactions';
import Input from './ui/Input';
import Button from './ui/Button';
import Card from './ui/Card';
import type { Transaction, TransactionType, TransactionFormData } from '../lib/types';

const transactionSchema = z.object({
  type: z.enum(['income', 'expense', 'transfer']),
  amount: z.coerce.number().positive('Amount must be positive'),
  description: z.string().default(''),
  walletId: z.string().min(1, 'Please select a wallet'),
  toWalletId: z.string().optional(),
  category: z.string().min(1, 'Please select a category'),
}).refine(data => {
  if (data.type === 'transfer') {
    return data.toWalletId && data.walletId !== data.toWalletId;
  }
  return true;
}, {
  message: 'Source and destination wallets must be different',
  path: ['toWalletId'],
});

interface TransactionFormProps {
  transactionToEdit?: Transaction;
  onFinished: () => void;
}

export default function TransactionForm({ transactionToEdit, onFinished }: TransactionFormProps) {
  const { wallets, categories, addTransaction, editTransaction } = useTransactions();
  const [selectedType, setSelectedType] = useState<TransactionType>('expense');
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<TransactionFormData>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      type: 'expense',
      amount: 0,
      description: '',
      walletId: '',
      toWalletId: '',
      category: '',
    },
  });

  const watchedType = watch('type');
  const watchedWalletId = watch('walletId');

  useEffect(() => {
    if (transactionToEdit) {
      reset({
        type: transactionToEdit.type,
        amount: transactionToEdit.amount,
        description: transactionToEdit.description,
        walletId: transactionToEdit.walletId,
        toWalletId: transactionToEdit.toWalletId || '',
        category: transactionToEdit.category,
      });
      setSelectedType(transactionToEdit.type);
    }
  }, [transactionToEdit, reset]);

  useEffect(() => {
    setSelectedType(watchedType);
  }, [watchedType]);

  const onSubmit = async (data: TransactionFormData) => {
    setLoading(true);
    try {
      if (transactionToEdit) {
        await editTransaction(transactionToEdit.id, data);
      } else {
        await addTransaction(data);
      }
      onFinished();
    } catch (error) {
      Alert.alert('Error', 'Failed to save transaction');
    } finally {
      setLoading(false);
    }
  };

  const filteredCategories = categories.filter(
    c => c.type === selectedType || c.type === 'all'
  );

  const availableToWallets = wallets.filter(w => w.id !== watchedWalletId);

  const typeOptions = [
    { value: 'expense', label: 'Expense', icon: 'trending-down', color: '#F44336' },
    { value: 'income', label: 'Income', icon: 'trending-up', color: '#4CAF50' },
    { value: 'transfer', label: 'Transfer', icon: 'swap-horiz', color: '#2196F3' },
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Transaction Type */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Transaction Type</Text>
        <View style={styles.typeContainer}>
          {typeOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.typeButton,
                selectedType === option.value && [
                  styles.typeButtonActive,
                  { borderColor: option.color }
                ]
              ]}
              onPress={() => {
                setValue('type', option.value as TransactionType);
                setValue('category', ''); // Reset category when type changes
              }}
            >
              <MaterialIcons 
                name={option.icon as any} 
                size={24} 
                color={selectedType === option.value ? option.color : '#666'} 
              />
              <Text style={[
                styles.typeButtonText,
                selectedType === option.value && { color: option.color }
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Amount */}
      <Card style={styles.section}>
        <Controller
          control={control}
          name="amount"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Amount"
              placeholder="0.00"
              keyboardType="numeric"
              value={value?.toString() || ''}
              onChangeText={(text) => onChange(parseFloat(text) || 0)}
              error={errors.amount?.message}
              icon="attach-money"
            />
          )}
        />
      </Card>

      {/* Wallet Selection */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>
          {selectedType === 'transfer' ? 'From Wallet' : 'Wallet'}
        </Text>
        <Controller
          control={control}
          name="walletId"
          render={({ field: { onChange, value } }) => (
            <View style={styles.walletGrid}>
              {wallets.map((wallet) => (
                <TouchableOpacity
                  key={wallet.id}
                  style={[
                    styles.walletButton,
                    value === wallet.id && styles.walletButtonActive
                  ]}
                  onPress={() => onChange(wallet.id)}
                >
                  <MaterialIcons name="account-balance-wallet" size={20} color="#007AFF" />
                  <Text style={styles.walletButtonText} numberOfLines={1}>
                    {wallet.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        />
        {errors.walletId && (
          <Text style={styles.errorText}>{errors.walletId.message}</Text>
        )}
      </Card>

      {/* To Wallet (for transfers) */}
      {selectedType === 'transfer' && (
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>To Wallet</Text>
          <Controller
            control={control}
            name="toWalletId"
            render={({ field: { onChange, value } }) => (
              <View style={styles.walletGrid}>
                {availableToWallets.map((wallet) => (
                  <TouchableOpacity
                    key={wallet.id}
                    style={[
                      styles.walletButton,
                      value === wallet.id && styles.walletButtonActive
                    ]}
                    onPress={() => onChange(wallet.id)}
                  >
                    <MaterialIcons name="account-balance-wallet" size={20} color="#007AFF" />
                    <Text style={styles.walletButtonText} numberOfLines={1}>
                      {wallet.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          />
          {errors.toWalletId && (
            <Text style={styles.errorText}>{errors.toWalletId.message}</Text>
          )}
        </Card>
      )}

      {/* Category (not for transfers) */}
      {selectedType !== 'transfer' && (
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Category</Text>
          <Controller
            control={control}
            name="category"
            render={({ field: { onChange, value } }) => (
              <View style={styles.categoryGrid}>
                {filteredCategories.map((category) => (
                  <TouchableOpacity
                    key={category.value}
                    style={[
                      styles.categoryButton,
                      value === category.value && styles.categoryButtonActive
                    ]}
                    onPress={() => onChange(category.value)}
                  >
                    <MaterialIcons name="category" size={16} color="#007AFF" />
                    <Text style={styles.categoryButtonText} numberOfLines={1}>
                      {category.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          />
          {errors.category && (
            <Text style={styles.errorText}>{errors.category.message}</Text>
          )}
        </Card>
      )}

      {/* Description */}
      <Card style={styles.section}>
        <Controller
          control={control}
          name="description"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Description (Optional)"
              placeholder="Enter description..."
              value={value}
              onChangeText={onChange}
              multiline
              numberOfLines={3}
              icon="description"
            />
          )}
        />
      </Card>

      {/* Submit Button */}
      <View style={styles.buttonContainer}>
        <Button
          title={transactionToEdit ? 'Update Transaction' : 'Add Transaction'}
          onPress={handleSubmit(onSubmit)}
          loading={loading}
          size="large"
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    backgroundColor: '#f9f9f9',
  },
  typeButtonActive: {
    backgroundColor: '#fff',
    borderWidth: 2,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginTop: 4,
  },
  walletGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  walletButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginHorizontal: '1%',
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#f9f9f9',
  },
  walletButtonActive: {
    borderColor: '#007AFF',
    backgroundColor: '#E3F2FD',
  },
  walletButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  categoryButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    marginHorizontal: '1%',
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#f9f9f9',
  },
  categoryButtonActive: {
    borderColor: '#007AFF',
    backgroundColor: '#E3F2FD',
  },
  categoryButtonText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#333',
    marginLeft: 6,
    flex: 1,
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 4,
  },
  buttonContainer: {
    paddingVertical: 20,
  },
});
