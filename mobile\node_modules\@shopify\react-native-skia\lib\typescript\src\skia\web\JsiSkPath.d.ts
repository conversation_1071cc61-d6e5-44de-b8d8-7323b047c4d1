import type { CanvasKit, Path } from "canvaskit-wasm";
import type { FillType, PathCommand, PathOp, SkMatrix, SkPath, SkPoint, SkRect, InputRRect, StrokeOpts, InputMatrix } from "../types";
import { HostObject } from "./Host";
import { JsiSkPoint } from "./JsiSkPoint";
import { JsiSkRect } from "./JsiSkRect";
export declare class JsiSkPath extends HostObject<Path, "Path"> implements SkPath {
    constructor(CanvasKit: CanvasKit, ref: Path);
    dispose: () => void;
    addPath(src: SkPath, matrix?: SkMatrix, extend?: boolean): this;
    addArc(oval: SkRect, startAngleInDegrees: number, sweepAngleInDegrees: number): this;
    addOval(oval: SkRect, isCCW?: boolean, startIndex?: number): this;
    countPoints(): number;
    addPoly(points: SkPoint[], close: boolean): this;
    moveTo(x: number, y: number): this;
    lineTo(x: number, y: number): this;
    makeAsWinding(): this | null;
    offset(dx: number, dy: number): this;
    rArcTo(rx: number, ry: number, xAxisRotateInDegrees: number, useSmallArc: boolean, isCCW: boolean, dx: number, dy: number): this;
    rConicTo(dx1: number, dy1: number, dx2: number, dy2: number, w: number): this;
    rCubicTo(cpx1: number, cpy1: number, cpx2: number, cpy2: number, x: number, y: number): this;
    rMoveTo(x: number, y: number): this;
    rLineTo(x: number, y: number): this;
    rQuadTo(x1: number, y1: number, x2: number, y2: number): this;
    setFillType(fill: FillType): this;
    setIsVolatile(volatile: boolean): this;
    stroke(opts?: StrokeOpts): this | null;
    close(): this;
    reset(): this;
    rewind(): this;
    computeTightBounds(): SkRect;
    arcToOval(oval: SkRect, startAngleInDegrees: number, sweepAngleInDegrees: number, forceMoveTo: boolean): this;
    arcToRotated(rx: number, ry: number, xAxisRotateInDegrees: number, useSmallArc: boolean, isCCW: boolean, x: number, y: number): this;
    arcToTangent(x1: number, y1: number, x2: number, y2: number, radius: number): this;
    conicTo(x1: number, y1: number, x2: number, y2: number, w: number): this;
    contains(x: number, y: number): boolean;
    copy(): JsiSkPath;
    cubicTo(cpx1: number, cpy1: number, cpx2: number, cpy2: number, x: number, y: number): this;
    dash(on: number, off: number, phase: number): boolean;
    equals(other: SkPath): boolean;
    getBounds(): JsiSkRect;
    getFillType(): number;
    quadTo(x1: number, y1: number, x2: number, y2: number): this;
    addRect(rect: SkRect, isCCW?: boolean): this;
    addRRect(rrect: InputRRect, isCCW?: boolean): this;
    getPoint(index: number): JsiSkPoint;
    isEmpty(): boolean;
    isVolatile(): boolean;
    addCircle(x: number, y: number, r: number): this;
    getLastPt(): JsiSkPoint;
    op(path: SkPath, op: PathOp): boolean;
    simplify(): boolean;
    toSVGString(): string;
    trim(start: number, stop: number, isComplement: boolean): this | null;
    transform(m: InputMatrix): this;
    interpolate(end: SkPath, t: number, output?: SkPath): SkPath | JsiSkPath | null;
    isInterpolatable(path2: SkPath): boolean;
    toCmds(): PathCommand[];
}
