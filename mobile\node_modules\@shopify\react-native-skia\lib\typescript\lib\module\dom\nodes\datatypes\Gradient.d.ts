export function transformOrigin(origin: any, transform: any): any[];
export function processColor(Skia: any, color: any): any;
export function processGradientProps(Skia: any, { colors, positions, mode, flags, ...transform }: {
    [x: string]: any;
    colors: any;
    positions: any;
    mode: any;
    flags: any;
}): {
    colors: any;
    positions: any;
    mode: any;
    flags: any;
    localMatrix: any;
};
export function getRect(Skia: any, props: any): any;
