export const __esModule: boolean;
export function drawLine(ctx: any, props: any): void;
export function drawOval(ctx: any, props: any): void;
export function drawImage(ctx: any, props: any): void;
export function drawPoints(ctx: any, props: any): void;
export function drawVertices(ctx: any, props: any): void;
export function drawDiffRect(ctx: any, props: any): void;
export function drawTextPath(ctx: any, props: any): void;
export function drawText(ctx: any, props: any): void;
export function drawPatch(ctx: any, props: any): void;
export function drawPath(ctx: any, props: any): void;
export function drawRect(ctx: any, props: any): void;
export function drawRRect(ctx: any, props: any): void;
export function drawTextBlob(ctx: any, props: any): void;
export function drawGlyphs(ctx: any, props: any): void;
export function drawImageSVG(ctx: any, props: any): void;
export function drawParagraph(ctx: any, props: any): void;
export function drawPicture(ctx: any, props: any): void;
export function drawAtlas(ctx: any, props: any): void;
export function drawCircle(ctx: any, props: any): void;
export function drawSkottie(ctx: any, props: any): void;
