export const __esModule: boolean;
export class JsiSkTextBlobFactory extends _Host.Host {
    MakeFromText(str: any, font: any): _JsiSkTextBlob.JsiSkTextBlob;
    MakeFromGlyphs(glyphs: any, font: any): _JsiSkTextBlob.JsiSkTextBlob;
    MakeFromRSXform(str: any, rsxforms: any, font: any): _JsiSkTextBlob.JsiSkTextBlob;
    MakeFromRSXformGlyphs(glyphs: any, rsxforms: any, font: any): _JsiSkTextBlob.JsiSkTextBlob;
}
import _Host = require("./Host");
import _JsiSkTextBlob = require("./JsiSkTextBlob");
