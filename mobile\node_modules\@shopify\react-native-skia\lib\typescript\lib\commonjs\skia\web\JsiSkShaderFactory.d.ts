export const __esModule: boolean;
export class JsiSkShaderFactory extends _Host.Host {
    MakeLinearGradient(start: any, end: any, colors: any, pos: any, mode: any, localMatrix: any, flags: any): _JsiSkShader.JsiSkShader;
    MakeRadialGradient(center: any, radius: any, colors: any, pos: any, mode: any, localMatrix: any, flags: any): _JsiSkShader.JsiSkShader;
    MakeTwoPointConicalGradient(start: any, startRadius: any, end: any, endRadius: any, colors: any, pos: any, mode: any, localMatrix: any, flags: any): _JsiSkShader.JsiSkShader;
    MakeSweepGradient(cx: any, cy: any, colors: any, pos: any, mode: any, localMatrix: any, flags: any, startAngleInDegrees: any, endAngleInDegrees: any): _JsiSkShader.JsiSkShader;
    MakeTurbulence(baseFreqX: any, baseFreqY: any, octaves: any, seed: any, tileW: any, tileH: any): _JsiSkShader.JsiSkShader;
    MakeFractalNoise(baseFreqX: any, baseFreqY: any, octaves: any, seed: any, tileW: any, tileH: any): _JsiSkShader.JsiSkShader;
    MakeBlend(mode: any, one: any, two: any): _JsiSkShader.JsiSkShader;
    MakeColor(color: any): _JsiSkShader.JsiSkShader;
}
import _Host = require("./Host");
import _JsiSkShader = require("./JsiSkShader");
