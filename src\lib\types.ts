
import type { LucideIcon } from 'lucide-react';

export interface Wallet {
  id: string;
  name: string;
  icon: string; // Icon name from WALLET_ICONS
  balance: number;
  currency: string;
}

export type TransactionType = 'income' | 'expense' | 'transfer';

export interface Transaction {
  id: string;
  type: TransactionType;
  amount: number;
  category: string;
  description: string;
  walletId: string; // "from" wallet for transfers
  toWalletId?: string; // "to" wallet for transfers
  date: Date;
}

export interface CategoryInfo {
    value: string;
    label: string;
    iconName: string;
    type: TransactionType | 'all';
}

export interface Category extends Omit<CategoryInfo, 'iconName'> {
    icon: React.ComponentType<{ className?: string }>;
}

export interface WalletIconInfo {
    name: string;
    icon: LucideIcon;
}

export interface Budget {
    id: string;
    category: string;
    amount: number;
}

export interface Subscription {
    tier: 'free' | 'premium';
}
