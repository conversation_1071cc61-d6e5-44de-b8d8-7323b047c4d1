"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { CheckCircle, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

function SuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('order_id');

  const handleGoToDashboard = () => {
    router.push('/');
  };

  const handleGoToSubscription = () => {
    router.push('/subscription');
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="bg-card-gradient max-w-md w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-500/10">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <CardTitle className="text-2xl">Payment Successful!</CardTitle>
          <CardDescription>
            Welcome to ExpenseFlow Premium! You now have lifetime access to all premium features.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-muted/50 rounded-lg p-4">
            <h3 className="font-semibold mb-2">What's included in Premium:</h3>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• Unlimited Wallets</li>
              <li>• Unlimited Categories</li>
              <li>• Unlimited History</li>
              <li>• Advanced Analytics</li>
              <li>• AI Description Suggestions</li>
              <li>• Priority Support</li>
            </ul>
          </div>
          
          {orderId && (
            <div className="text-xs text-muted-foreground text-center">
              Order ID: {orderId}
            </div>
          )}
          
          <div className="flex flex-col gap-3">
            <Button onClick={handleGoToDashboard} className="w-full">
              Go to Dashboard
            </Button>
            <Button onClick={handleGoToSubscription} variant="outline" className="w-full">
              <ArrowLeft className="mr-2 h-4 w-4" />
              View Subscription
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function SubscriptionSuccessPage() {
  return (
    <React.Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="bg-card-gradient max-w-md w-full">
          <CardContent className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    }>
      <SuccessContent />
    </React.Suspense>
  );
}
