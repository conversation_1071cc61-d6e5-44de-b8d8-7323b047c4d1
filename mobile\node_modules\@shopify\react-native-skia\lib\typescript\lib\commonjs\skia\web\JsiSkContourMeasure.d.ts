export const __esModule: boolean;
export class JsiSkContourMeasure extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    getPosTan(distance: any): _JsiSkPoint.JsiSkPoint[];
    getSegment(startD: any, stopD: any, startWithMoveTo: any): _JsiSkPath.JsiSkPath;
    isClosed(): any;
    length(): any;
}
import _Host = require("./Host");
import _JsiSkPoint = require("./JsiSkPoint");
import _JsiSkPath = require("./JsiSkPath");
