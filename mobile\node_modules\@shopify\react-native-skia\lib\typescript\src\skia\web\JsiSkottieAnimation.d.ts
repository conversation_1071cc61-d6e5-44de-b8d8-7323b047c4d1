import type { CanvasKit, ManagedSkottieAnimation } from "canvaskit-wasm";
import type { SkSkottieAnimation, SlotInfo, SlottableTextProperty } from "../types/Skottie";
import type { SkColor, SkPoint, SkRect } from "../types";
import { HostObject } from "./Host";
import type { JsiSkCanvas } from "./JsiSkCanvas";
import { JsiSkRect } from "./JsiSkRect";
export declare class JsiSkottieAnimation extends HostObject<ManagedSkottieAnimation, "SkottieAnimation"> implements SkSkottieAnimation {
    constructor(CanvasKit: CanvasKit, ref: ManagedSkottieAnimation);
    getOpacityProps(): import("canvaskit-wasm").OpacityProperty[];
    getTextProps(): import("canvaskit-wasm").TextProperty[];
    getColorProps(): {
        key: string;
        value: SkColor;
    }[];
    getTransformProps(): {
        key: string;
        value: {
            anchor: {
                x: number;
                y: number;
            };
            position: {
                x: number;
                y: number;
            };
            scale: {
                x: number;
                y: number;
            };
            rotation: number;
            skew: number;
            skewAxis: number;
        };
    }[];
    setColor(key: string, color: SkColor): boolean;
    setText(key: string, text: string, size: number): boolean;
    setOpacity(key: string, opacity: number): boolean;
    setTransform(key: string, anchor: SkPoint, position: SkPoint, scale: SkPoint, rotation: number, skew: number, skewAxis: number): boolean;
    getSlotInfo(): SlotInfo;
    setColorSlot(key: string, color: SkColor): boolean;
    setScalarSlot(key: string, scalar: number): boolean;
    setVec2Slot(key: string, vec2: SkPoint): boolean;
    setTextSlot(key: string, text: SlottableTextProperty): boolean;
    setImageSlot(key: string, assetName: string): boolean;
    getColorSlot(key: string): SkColor | null;
    getScalarSlot(key: string): number | null;
    getVec2Slot(key: string): SkPoint | null;
    getTextSlot(key: string): SlottableTextProperty | null;
    duration(): number;
    fps(): number;
    render(canvas: JsiSkCanvas, dstRect?: SkRect): void;
    seekFrame(frame: number, damageRect?: JsiSkRect): void;
    size(): {
        width: number;
        height: number;
    };
    version(): string;
    dispose(): void;
}
