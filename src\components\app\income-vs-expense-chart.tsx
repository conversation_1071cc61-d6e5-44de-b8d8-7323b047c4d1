
"use client"

import * as React from "react"
import { TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, LabelList, XAxis, YAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartConfig,
} from "@/components/ui/chart"
import { useTransactions } from "@/hooks/use-transactions"
import { formatCurrency } from "@/lib/utils"
import type { Transaction } from "@/lib/types"

const chartConfig = {
    income: {
        label: "Income",
        color: "hsl(var(--chart-2))",
    },
    expense: {
        label: "Expense",
        color: "hsl(var(--destructive))",
    },
} satisfies ChartConfig

interface IncomeVsExpenseChartProps {
    transactions: Transaction[];
}

export function IncomeVsExpenseChart({ transactions }: IncomeVsExpenseChartProps) {
    const totals = React.useMemo(() => {
        return transactions.reduce((acc, tx) => {
            acc[tx.type] += tx.amount;
            return acc;
        }, { income: 0, expense: 0 });
    }, [transactions])

    const chartData = [
        { type: 'income', amount: totals.income, fill: "var(--color-income)" },
        { type: 'expense', amount: totals.expense, fill: "var(--color-expense)" },
    ]

  return (
    <Card className="bg-card-gradient">
      <CardHeader>
        <CardTitle>Income vs. Expense</CardTitle>
        <CardDescription>Your total income and expenses for this period.</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[150px] w-full">
          <BarChart
            accessibilityLayer
            data={chartData}
            layout="vertical"
            margin={{ left: 10, right: 50 }}
          >
            <CartesianGrid horizontal={false} />
            <YAxis
                dataKey="type"
                type="category"
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                tickFormatter={(value) => value.charAt(0).toUpperCase() + value.slice(1)}
            />
            <XAxis dataKey="amount" type="number" hide />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent />}
            />
            <Bar dataKey="amount" radius={5}>
                <LabelList
                    position="right"
                    offset={10}
                    className="fill-foreground"
                    fontSize={12}
                    formatter={(value: number) => formatCurrency(value)}
                />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
       <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          <TrendingUp className="h-4 w-4 text-green-400" /> Total Income: {formatCurrency(totals.income)}
        </div>
        <div className="flex gap-2 font-medium leading-none text-muted-foreground">
          <TrendingDown className="h-4 w-4 text-red-400" /> Total Expense: {formatCurrency(totals.expense)}
        </div>
      </CardFooter>
    </Card>
  )
}
