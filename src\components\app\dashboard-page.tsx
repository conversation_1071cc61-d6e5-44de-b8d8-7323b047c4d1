
"use client";

import * as React from "react";
import { TransactionList } from "@/components/app/transaction-list";
import { useTransactions } from "@/hooks/use-transactions";
import { isSameMonth } from "date-fns";
import { Skeleton } from "../ui/skeleton";
import { Card, CardContent, CardHeader } from "../ui/card";
import { History } from "lucide-react";
import type { Transaction } from "@/lib/types";

interface DashboardPageProps {
  onEditTransaction: (transaction: Transaction) => void;
}

export default function DashboardPage({ onEditTransaction }: DashboardPageProps) {
  const { transactions, wallets, isInitialized } = useTransactions();
  
  const currentMonthTransactions = React.useMemo(() => {
    const now = new Date();
    return transactions.filter(t => isSameMonth(t.date, now));
  }, [transactions]);

  const [totalIncome, totalExpense] = React.useMemo(() => {
    return currentMonthTransactions.reduce(
      (acc, transaction) => {
        if (transaction.type === 'income') {
          acc[0] += transaction.amount;
        } else if (transaction.type === 'expense') {
          acc[1] += transaction.amount;
        }
        return acc;
      },
      [0, 0]
    );
  }, [currentMonthTransactions]);

  if (!isInitialized) {
    return (
        <Card className="bg-card-gradient h-full">
            <CardHeader>
              <Skeleton className="h-8 w-1/3" />
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 mt-2">
                <Skeleton className="h-5 w-40" />
                <Skeleton className="h-5 w-40" />
              </div>
            </CardHeader>
            <CardContent className="p-0">
                <div className="p-1 sm:p-4 space-y-4">
                    {[...Array(4)].map((_, i) => (
                      <Skeleton key={i} className="h-16 w-full" />
                    ))}
                </div>
            </CardContent>
        </Card>
    )
  }

  if (currentMonthTransactions.length === 0) {
    return (
        <Card className="bg-card-gradient h-full">
            <CardContent className="flex flex-col items-center justify-center p-10 text-center h-full">
                <History className="w-16 h-16 mb-4 text-muted-foreground" />
                <h3 className="text-xl font-semibold">No Transactions This Month</h3>
                <p className="text-muted-foreground mt-2">Add a new transaction to see your history.</p>
            </CardContent>
        </Card>
    );
  }

  return (
    <TransactionList 
      transactions={currentMonthTransactions} 
      wallets={wallets}
      totalIncome={totalIncome}
      totalExpense={totalExpense}
      onEdit={onEditTransaction}
    />
  );
}
