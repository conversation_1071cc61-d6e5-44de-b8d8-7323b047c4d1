
"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartConfig,
} from "@/components/ui/chart"
import { useTransactions } from "@/hooks/use-transactions"
import { Badge } from "../ui/badge"
import type { Transaction } from "@/lib/types"

const chartConfig = {} satisfies ChartConfig
const COLORS = [
    "hsl(var(--chart-1))", 
    "hsl(var(--chart-2))",
    "hsl(var(--chart-3))",
    "hsl(var(--chart-4))",
    "hsl(var(--chart-5))",
    "hsl(var(--primary))",
    "hsl(var(--accent))",
    "hsl(var(--secondary))"
];

interface CategorySpendingProps {
    transactions: Transaction[];
}

export function CategorySpending({ transactions }: CategorySpendingProps) {
    const { categories } = useTransactions();
    const categoryData = React.useMemo(() => {
        const expenseTransactions = transactions.filter(t => t.type === 'expense');
        const dataMap = new Map<string, number>()
        expenseTransactions.forEach(tx => {
            const currentAmount = dataMap.get(tx.category) || 0
            dataMap.set(tx.category, currentAmount + tx.amount)
        })

        return Array.from(dataMap.entries())
            .map(([category, amount]) => ({
                name: categories.find(c => c.value === category)?.label || category,
                value: amount,
                fill: COLORS[categories.findIndex(c => c.value === category) % COLORS.length]
            }))
            .sort((a,b) => b.value - a.value)

    }, [transactions, categories])


  return (
    <Card className="bg-card-gradient flex flex-col">
      <CardHeader>
        <CardTitle>Category Spending</CardTitle>
        <CardDescription>Breakdown of expenses by category</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col items-center justify-center">
        {categoryData.length === 0 ? (
          <p className="text-muted-foreground">No expense data for this period.</p>
        ) : (
          <>
            <ChartContainer config={chartConfig} className="w-full aspect-square">
              <PieChart>
                <Pie data={categoryData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} label>
                     {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                </Pie>
                <ChartTooltip content={<ChartTooltipContent />} />
              </PieChart>
            </ChartContainer>
            <div className="flex flex-wrap justify-center gap-2 mt-4">
                {categoryData.slice(0, 5).map((entry, index) => (
                    <Badge key={index} variant="outline" style={{borderColor: entry.fill}}>
                        <div className="w-2 h-2 rounded-full mr-2" style={{backgroundColor: entry.fill}} />
                        {entry.name}
                    </Badge>
                ))}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
