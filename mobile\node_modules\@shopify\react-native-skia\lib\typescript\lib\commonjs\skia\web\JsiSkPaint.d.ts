export const __esModule: boolean;
export class JsiSkPaint extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    copy(): JsiSkPaint;
    assign(paint: any): void;
    reset(): void;
    getAlphaf(): any;
    getColor(): any;
    getStrokeCap(): any;
    getStrokeJoin(): any;
    getStrokeMiter(): any;
    getStrokeWidth(): any;
    setAlphaf(alpha: any): void;
    setAntiAlias(aa: any): void;
    setDither(dither: any): void;
    setBlendMode(blendMode: any): void;
    setColor(color: any): void;
    setColorFilter(filter: any): void;
    setImageFilter(filter: any): void;
    setMaskFilter(filter: any): void;
    setPathEffect(effect: any): void;
    setShader(shader: any): void;
    setStrokeCap(cap: any): void;
    setStrokeJoin(join: any): void;
    setStrokeMiter(limit: any): void;
    setStrokeWidth(width: any): void;
    setStyle(style: any): void;
}
import _Host = require("./Host");
