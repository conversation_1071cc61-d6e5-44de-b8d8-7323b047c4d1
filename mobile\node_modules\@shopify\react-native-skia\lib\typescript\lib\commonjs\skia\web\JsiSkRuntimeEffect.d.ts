export const __esModule: boolean;
export class JsiSkRuntimeEffect extends _Host.HostObject {
    sksl: any;
    source(): any;
    makeShader(uniforms: any, localMatrix: any): _JsiSkShader.JsiSkShader;
    makeShaderWithChildren(uniforms: any, children: any, localMatrix: any): _JsiSkShader.JsiSkShader;
    getUniform(index: any): any;
    getUniformCount(): any;
    getUniformFloatCount(): any;
    getUniformName(index: any): any;
}
import _Host = require("./Host");
import _JsiSkShader = require("./JsiSkShader");
