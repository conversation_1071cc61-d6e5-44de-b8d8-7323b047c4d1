@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 211 40% 17%;
    --card: 0 0% 100%;
    --card-foreground: 211 40% 17%;
    --popover: 0 0% 100%;
    --popover-foreground: 211 40% 17%;
    --primary: 217 100% 50%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 325 100% 50%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 217 100% 50%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background-start-rgb: 21 28 41;
    --background-end-rgb: 41 21 46;
    --background: 211 40% 17%;
    --foreground: 0 0% 98%;
    --card: 211 35% 25%;
    --card-foreground: 0 0% 98%;
    --popover: 211 40% 17%;
    --popover-foreground: 0 0% 98%;
    --primary: 217 100% 50%;
    --primary-foreground: 0 0% 98%;
    --secondary: 211 30% 25%;
    --secondary-foreground: 0 0% 98%;
    --muted: 211 30% 25%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 325 100% 50%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 211 30% 30%;
    --input: 211 30% 30%;
    --ring: 217 100% 50%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  .dark body {
    background: radial-gradient(circle at top, rgb(var(--background-start-rgb)), rgb(var(--background-end-rgb)));
  }
}

@layer components {
    .bg-card-gradient {
        @apply bg-card/60 backdrop-blur-lg border border-border/20 shadow-lg;
    }
}
