"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { loading, isSignedIn } = useAuth();
  const router = useRouter();

  React.useEffect(() => {
    if (!loading && !isSignedIn) {
      router.push('/login');
    }
  }, [loading, isSignedIn, router]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Don't render children if not signed in (will redirect)
  if (!isSignedIn) {
    return null;
  }

  return <>{children}</>;
}
