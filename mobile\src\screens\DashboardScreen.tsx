import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { isSameMonth } from 'date-fns';
import { useTransactions } from '../hooks/useTransactions';
import { formatCurrency } from '../lib/utils';
import Card from '../components/ui/Card';
import TransactionList from '../components/TransactionList';
import TransactionForm from '../components/TransactionForm';
import Modal from '../components/ui/Modal';
import type { Transaction } from '../lib/types';

export default function DashboardScreen() {
  const { transactions, wallets, isInitialized } = useTransactions();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [transactionToEdit, setTransactionToEdit] = useState<Transaction | undefined>();

  const currentMonthTransactions = useMemo(() => {
    const now = new Date();
    return transactions.filter(t => isSameMonth(t.date, now));
  }, [transactions]);

  const [totalIncome, totalExpense] = useMemo(() => {
    return currentMonthTransactions.reduce(
      (acc, transaction) => {
        if (transaction.type === 'income') {
          acc[0] += transaction.amount;
        } else if (transaction.type === 'expense') {
          acc[1] += transaction.amount;
        }
        return acc;
      },
      [0, 0]
    );
  }, [currentMonthTransactions]);

  const totalBalance = useMemo(() => {
    return wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
  }, [wallets]);

  const handleAddTransaction = () => {
    setTransactionToEdit(undefined);
    setIsFormOpen(true);
  };

  const handleEditTransaction = (transaction: Transaction) => {
    setTransactionToEdit(transaction);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setTransactionToEdit(undefined);
  };

  if (!isInitialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <Card style={styles.summaryCard}>
            <View style={styles.summaryHeader}>
              <MaterialIcons name="account-balance-wallet" size={24} color="#007AFF" />
              <Text style={styles.summaryTitle}>Total Balance</Text>
            </View>
            <Text style={styles.summaryAmount}>{formatCurrency(totalBalance)}</Text>
          </Card>

          <View style={styles.summaryRow}>
            <Card style={[styles.summaryCard, styles.halfCard]}>
              <View style={styles.summaryHeader}>
                <MaterialIcons name="trending-up" size={20} color="#4CAF50" />
                <Text style={styles.summaryTitleSmall}>Income</Text>
              </View>
              <Text style={[styles.summaryAmount, styles.incomeText]}>
                {formatCurrency(totalIncome)}
              </Text>
            </Card>

            <Card style={[styles.summaryCard, styles.halfCard]}>
              <View style={styles.summaryHeader}>
                <MaterialIcons name="trending-down" size={20} color="#F44336" />
                <Text style={styles.summaryTitleSmall}>Expenses</Text>
              </View>
              <Text style={[styles.summaryAmount, styles.expenseText]}>
                {formatCurrency(totalExpense)}
              </Text>
            </Card>
          </View>
        </View>

        {/* Transactions */}
        <Card style={styles.transactionsCard}>
          <View style={styles.transactionsHeader}>
            <Text style={styles.transactionsTitle}>Recent Transactions</Text>
            <Text style={styles.transactionsCount}>
              {currentMonthTransactions.length} this month
            </Text>
          </View>
          
          {currentMonthTransactions.length === 0 ? (
            <View style={styles.emptyState}>
              <MaterialIcons name="receipt" size={48} color="#ccc" />
              <Text style={styles.emptyStateText}>No transactions this month</Text>
              <Text style={styles.emptyStateSubtext}>
                Add a new transaction to get started
              </Text>
            </View>
          ) : (
            <TransactionList
              transactions={currentMonthTransactions}
              wallets={wallets}
              onEdit={handleEditTransaction}
              showHeader={false}
            />
          )}
        </Card>
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab} onPress={handleAddTransaction}>
        <MaterialIcons name="add" size={28} color="#fff" />
      </TouchableOpacity>

      {/* Transaction Form Modal */}
      <Modal
        visible={isFormOpen}
        onClose={handleCloseForm}
        title={transactionToEdit ? 'Edit Transaction' : 'Add Transaction'}
        fullScreen
      >
        <TransactionForm
          transactionToEdit={transactionToEdit}
          onFinished={handleCloseForm}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    marginBottom: 16,
  },
  summaryCard: {
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfCard: {
    flex: 0.48,
    marginBottom: 0,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginLeft: 8,
  },
  summaryTitleSmall: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginLeft: 6,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  incomeText: {
    color: '#4CAF50',
    fontSize: 20,
  },
  expenseText: {
    color: '#F44336',
    fontSize: 20,
  },
  transactionsCard: {
    flex: 1,
    marginBottom: 80,
  },
  transactionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  transactionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  transactionsCount: {
    fontSize: 14,
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});
