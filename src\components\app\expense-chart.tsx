
"use client"

import * as React from "react"
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts"
import { format, eachDayOfInterval, startOfMonth, endOfMonth, isSameDay } from "date-fns"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartConfig,
} from "@/components/ui/chart"
import { useTransactions } from "@/hooks/use-transactions"
import { formatCurrency } from "@/lib/utils"
import type { Transaction } from "@/lib/types"

const chartConfig = {
  expense: {
    label: "Expense",
    color: "hsl(var(--destructive))",
  },
  income: {
    label: "Income",
    color: "hsl(var(--chart-2))",
  }
} satisfies ChartConfig

interface ExpenseChartProps {
    transactions: Transaction[];
}

export function ExpenseChart({ transactions }: ExpenseChartProps) {
  const dailyData = React.useMemo(() => {
    if (transactions.length === 0) {
      const today = new Date();
      const monthStart = startOfMonth(today);
      const monthEnd = endOfMonth(today);
      return eachDayOfInterval({ start: monthStart, end: monthEnd }).map(date => ({
        date: format(date, "yyyy-MM-dd"),
        income: 0,
        expense: 0
      }));
    }

    const monthStart = startOfMonth(transactions[0].date);
    const monthEnd = endOfMonth(transactions[0].date);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

    return daysInMonth.map(day => {
      const dayTransactions = transactions.filter(tx => isSameDay(tx.date, day));
      const income = dayTransactions
        .filter(tx => tx.type === 'income')
        .reduce((sum, tx) => sum + tx.amount, 0);
      const expense = dayTransactions
        .filter(tx => tx.type === 'expense')
        .reduce((sum, tx) => sum + tx.amount, 0);

      return {
        date: format(day, "yyyy-MM-dd"),
        income,
        expense,
      };
    });
  }, [transactions]);

  return (
    <Card className="bg-card-gradient">
      <CardHeader>
        <CardTitle>Daily Overview</CardTitle>
        <CardDescription>Income and expenses for the selected month</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <BarChart accessibilityLayer data={dailyData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => format(new Date(value), "d")}
            />
            <YAxis tickFormatter={(value) => formatCurrency(value, "USD").slice(0, -3)} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar dataKey="income" fill="var(--color-income)" radius={4} />
            <Bar dataKey="expense" fill="var(--color-expense)" radius={4} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
