
"use client";

import * as React from "react";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Save, Loader2, PlusCircle, Trash2, ChevronsUpDown } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useTransactions } from "@/hooks/use-transactions";
import { useToast } from "@/hooks/use-toast";
import type { Budget } from "@/lib/types";
import { formatCurrency, cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import { isSameMonth } from "date-fns";
import { Skeleton } from "../ui/skeleton";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "../ui/command";

const budgetFormSchema = z.object({
  budgets: z.array(
    z.object({
      category: z.string(),
      amount: z.coerce.number().min(0, "Budget must be a positive number."),
    })
  ),
});

type BudgetFormValues = z.infer<typeof budgetFormSchema>;

export function BudgetManager() {
  const { budgets, setBudgets, transactions, categories, isInitialized, addBudget, removeBudget } = useTransactions();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = React.useState(false);

  const expenseCategories = React.useMemo(() => 
    categories.filter(c => c.type === 'expense'), 
  [categories]);

  const categorySpending = React.useMemo(() => {
    const spendingMap = new Map<string, number>();
    const now = new Date();
    transactions
      .filter(t => t.type === 'expense' && isSameMonth(t.date, now))
      .forEach(t => {
        spendingMap.set(t.category, (spendingMap.get(t.category) || 0) + t.amount);
      });
    return spendingMap;
  }, [transactions]);

  const defaultValues = React.useMemo(() => {
    return {
      budgets: budgets.map(budget => ({
          category: budget.category,
          amount: budget.amount,
      })),
    };
  }, [budgets]);
  
  const form = useForm<BudgetFormValues>({
    resolver: zodResolver(budgetFormSchema),
    defaultValues,
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "budgets",
  });
  
  React.useEffect(() => {
    if (isInitialized) {
        form.reset(defaultValues);
    }
  }, [budgets, defaultValues, form, isInitialized]);

  const onSubmit = (data: BudgetFormValues) => {
    setIsSaving(true);
    const newBudgets: Budget[] = data.budgets.map(b => ({
      id: `budget-${b.category}`,
      ...b
    }));
    setBudgets(newBudgets);
    
    setTimeout(() => {
        toast({
            title: "Budgets Saved!",
            description: "Your new budget limits have been successfully saved.",
        });
        setIsSaving(false);
    }, 500);
  };

  const handleRemoveBudget = (categoryValue: string, index: number) => {
    removeBudget(categoryValue);
    remove(index);
  }
  
  const handleAddBudget = (categoryValue: string) => {
    addBudget({ category: categoryValue, amount: 0 });
    append({ category: categoryValue, amount: 0 });
  }

  const budgetedCategories = form.watch('budgets').map(b => b.category);
  const unbudgetedCategories = expenseCategories.filter(c => !budgetedCategories.includes(c.value));

  const totalBudget = form.watch('budgets').reduce((total, b) => total + b.amount, 0);
  
  if (!isInitialized) {
    return (
      <Card className="bg-card-gradient">
        <CardHeader>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-10 w-24" />
              </div>
              <Skeleton className="h-4 w-full" />
              <div className="flex justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          ))}
        </CardContent>
        <CardFooter className="flex justify-between items-center">
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-10 w-32" />
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card className="bg-card-gradient">
      <CardHeader>
        <CardTitle>Set Your Budgets</CardTitle>
        <CardDescription>
          Manage your monthly spending limits for each category.
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
             {fields.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                    <p>No budgets set.</p>
                    <p>Add a category to start budgeting.</p>
                </div>
            )}
            {fields.map((field, index) => {
              const budgetInfo = form.getValues(`budgets.${index}`);
              const categoryInfo = expenseCategories.find(c => c.value === budgetInfo.category);
              if (!categoryInfo) return null;

              const spent = categorySpending.get(budgetInfo.category) || 0;
              const budgetAmount = form.watch(`budgets.${index}.amount`);
              const progress = budgetAmount > 0 ? (spent / budgetAmount) * 100 : 0;
              
              const Icon = categoryInfo.icon;
              return (
                <FormField
                  key={field.id}
                  control={form.control}
                  name={`budgets.${index}.amount`}
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <div className="flex items-center justify-between">
                        <FormLabel className="flex items-center gap-2">
                           <Icon className="h-5 w-5" />
                           {categoryInfo.label}
                        </FormLabel>
                        <div className="flex items-center gap-2">
                            <div className="w-28">
                                <FormControl>
                                <Input
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    {...field}
                                    className="text-right"
                                />
                                </FormControl>
                            </div>
                            <Button type="button" variant="ghost" size="icon" className="text-destructive h-8 w-8" onClick={() => handleRemoveBudget(categoryInfo.value, index)}>
                                <Trash2 className="h-4 w-4 text-red-400" />
                            </Button>
                        </div>
                      </div>
                      <div>
                        <Progress value={progress} className={cn(progress > 100 && "[&>div]:bg-destructive")} />
                        <div className="flex justify-between text-xs text-muted-foreground mt-1">
                          <span>{formatCurrency(spent)} spent</span>
                          <span className={cn(spent > budgetAmount && "text-destructive font-bold")}>
                            {formatCurrency(budgetAmount - spent)} remaining
                          </span>
                        </div>
                      </div>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />
              );
            })}
            <div className="pt-4">
                 <Popover>
                    <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full" disabled={unbudgetedCategories.length === 0}>
                            <PlusCircle className="mr-2 h-4 w-4" /> Add Budget Category
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                         <Command>
                            <CommandInput placeholder="Search category..." />
                            <CommandList>
                                <CommandEmpty>No categories available.</CommandEmpty>
                                <CommandGroup>
                                    {unbudgetedCategories.map((category) => (
                                        <CommandItem
                                            key={category.value}
                                            value={category.label}
                                            onSelect={() => handleAddBudget(category.value)}
                                        >
                                            <category.icon className="mr-2 h-4 w-4" />
                                            {category.label}
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            </CommandList>
                        </Command>
                    </PopoverContent>
                </Popover>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between items-center">
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Budgets
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
