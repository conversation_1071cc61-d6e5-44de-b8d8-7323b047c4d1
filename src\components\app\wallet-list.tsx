
"use client";

import * as React from "react";
import { MoreV<PERSON><PERSON>, Edit, Trash2, PlusCircle, Crown } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTransactions } from "@/hooks/use-transactions";
import { formatCurrency } from "@/lib/utils";
import { WalletDialog } from "./wallet-dialog";
import type { Wallet } from "@/lib/types";
import { Button } from "../ui/button";
import { WALLET_ICONS } from "@/lib/constants";
import { Skeleton } from "../ui/skeleton";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";

export function WalletList() {
  const { wallets, deleteWallet, editWallet, addWallet, isInitialized, subscription } = useTransactions();
  const [selectedWallet, setSelectedWallet] = React.useState<Wallet | undefined>(undefined);
  const [isEditOpen, setIsEditOpen] = React.useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = React.useState(false);
  const [isAddOpen, setIsAddOpen] = React.useState(false);

  const totalBalance = React.useMemo(() => {
    return wallets.reduce((sum, wallet) => sum + wallet.balance, 0)
  }, [wallets])

  const handleEditClick = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setIsEditOpen(true);
  };

  const handleDeleteClick = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setIsDeleteOpen(true);
  };

  const confirmDelete = () => {
    if (selectedWallet) {
      deleteWallet(selectedWallet.id);
    }
    setIsDeleteOpen(false);
    setSelectedWallet(undefined);
  };

  if (!isInitialized) {
    return (
      <Card className="bg-card-gradient">
        <CardHeader className="flex-row items-start justify-between">
          <div>
            <Skeleton className="h-8 w-40" />
            <Skeleton className="h-4 w-52 mt-2" />
          </div>
          <div className="text-right">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-32 mt-2" />
          </div>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(2)].map((_, i) => (
             <Skeleton key={i} className="h-24 w-full" />
          ))}
        </CardContent>
        <CardFooter className="flex flex-col items-center justify-center text-center p-6">
          <Skeleton className="h-10 w-40" />
        </CardFooter>
      </Card>
    );
  }
  
  const canAddWallet = subscription.tier === 'premium' || wallets.length < 3;
  
  const AddButton = (
    <Button onClick={() => setIsAddOpen(true)} disabled={!canAddWallet}>
      <PlusCircle className="mr-2 h-4 w-4" /> Add New Wallet
    </Button>
  );

  return (
    <>
      <Card className="bg-card-gradient">
        <CardHeader className="flex-row items-start justify-between">
          <div>
            <CardTitle>Manage Wallets</CardTitle>
            <CardDescription>Your wallets and their balances.</CardDescription>
          </div>
          <div className="text-right">
              <p className="text-sm text-muted-foreground">Total Balance</p>
              <p className="text-2xl font-bold">{formatCurrency(totalBalance)}</p>
          </div>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {wallets.map((wallet) => {
            const Icon = WALLET_ICONS.find(i => i.name === wallet.icon)?.icon || WALLET_ICONS[0].icon;
            return (
              <Card key={wallet.id} className="relative bg-card/50">
                <CardHeader className="flex flex-row items-center gap-4 space-y-0 pb-6">
                    <div className="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-primary/10">
                        <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                        <CardTitle className="text-xl">{wallet.name}</CardTitle>
                        <CardDescription>{formatCurrency(wallet.balance)}</CardDescription>
                    </div>
                </CardHeader>
                <div className="absolute top-4 right-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditClick(wallet)}>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Edit</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(wallet)}
                        className="text-destructive text-red-400"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </Card>
            );
          })}
        </CardContent>
        <CardFooter className="flex flex-col items-center justify-center text-center p-6">
          {wallets.length === 0 ? (
            <p className="text-muted-foreground mt-2">No wallets found. Add one to get started.</p>
          ) : null}
          {!canAddWallet ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>{AddButton}</TooltipTrigger>
                <TooltipContent>
                  <p className="flex items-center gap-2"><Crown className="h-4 w-4 text-yellow-400"/> Upgrade to Premium for unlimited wallets.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            AddButton
          )}
        </CardFooter>
      </Card>

      <WalletDialog
        open={isEditOpen}
        onOpenChange={setIsEditOpen}
        walletToEdit={selectedWallet}
        onSave={editWallet}
      >
        <span />
      </WalletDialog>
      
      <WalletDialog
        open={isAddOpen}
        onOpenChange={setIsAddOpen}
        onSave={addWallet}
      >
        <span />
      </WalletDialog>

      <AlertDialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the wallet "{selectedWallet?.name}". This action cannot be undone. All transactions associated with this wallet will also be deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
