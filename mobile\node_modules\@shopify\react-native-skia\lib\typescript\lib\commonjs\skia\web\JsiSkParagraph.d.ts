export const __esModule: boolean;
export class JsiSkParagraph extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    getMinIntrinsicWidth(): any;
    getMaxIntrinsicWidth(): any;
    getLongestLine(): any;
    layout(width: any): void;
    paint(canvas: any, x: any, y: any): void;
    getHeight(): any;
    getMaxWidth(): any;
    getGlyphPositionAtCoordinate(x: any, y: any): any;
    getRectsForPlaceholders(): any;
    getRectsForRange(start: any, end: any): any;
    getLineMetrics(): any;
    dispose(): void;
}
import _Host = require("./Host");
