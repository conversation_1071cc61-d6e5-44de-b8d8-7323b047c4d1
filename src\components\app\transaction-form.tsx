
"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Loader2, Sparkles, Check, ChevronsUpDown, ArrowRightLeft } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import type { Transaction } from "@/lib/types";
import { suggestTransactionDescription } from "@/ai/flows/suggest-transaction-description";
import { cn, formatCurrency } from "@/lib/utils";
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { useTransactions } from "@/hooks/use-transactions";
import { isSameMonth } from "date-fns";

const formSchema = z.object({
  type: z.enum(["income", "expense", "transfer"]),
  amount: z.coerce.number().positive("Amount must be positive."),
  description: z.string().default(""),
  walletId: z.string().min(1, "Please select a wallet."),
  toWalletId: z.string().optional(),
  category: z.string(),
}).refine(data => {
    if (data.type === 'transfer') {
        return data.toWalletId && data.walletId !== data.toWalletId;
    }
    return true;
}, {
    message: "Source and destination wallets must be different.",
    path: ["toWalletId"],
});

type FormValues = z.infer<typeof formSchema>;

interface TransactionFormProps {
    transactionToEdit?: Transaction;
    onFinished?: () => void;
}

export function TransactionForm({ transactionToEdit, onFinished }: TransactionFormProps) {
  const {wallets, transactions, budgets, addTransaction, editTransaction, categories} = useTransactions();
  const [activeTab, setActiveTab] = React.useState<"income" | "expense" | "transfer">("expense");
  const [isSuggesting, setIsSuggesting] = React.useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "expense",
      amount: 0,
      description: "",
      walletId: wallets[0]?.id || "",
      toWalletId: "",
      category: "",
    },
  });
  
  React.useEffect(() => {
    if (transactionToEdit) {
      form.reset({
        type: transactionToEdit.type,
        amount: transactionToEdit.amount,
        description: transactionToEdit.description,
        walletId: transactionToEdit.walletId,
        toWalletId: transactionToEdit.toWalletId,
        category: transactionToEdit.category,
      });
      setActiveTab(transactionToEdit.type);
    } else {
       form.reset({
        type: "expense",
        amount: 0,
        description: "",
        walletId: wallets[0]?.id || "",
        toWalletId: "",
        category: "",
      });
      setActiveTab("expense");
    }
  }, [transactionToEdit, form, wallets]);

  const handleTabChange = (value: string) => {
    const type = value as "income" | "expense" | "transfer";
    setActiveTab(type);
    form.setValue("type", type);
    form.setValue("category", type === 'transfer' ? 'transfer' : '');
    form.setValue("description", type === 'transfer' ? 'Wallet Transfer' : '');
  };

  const onSubmit = (data: FormValues) => {
    if (data.type === 'expense' && !transactionToEdit) {
      const budget = budgets.find(b => b.category === data.category);
      if (budget) {
        const now = new Date();
        const spent = transactions
          .filter(t => t.type === 'expense' && t.category === data.category && isSameMonth(t.date, now))
          .reduce((sum, t) => sum + t.amount, 0);
        
        if (spent + data.amount > budget.amount) {
          toast({
            variant: "destructive",
            title: "Budget Exceeded",
            description: `This transaction exceeds your budget for ${data.category} by ${formatCurrency(spent + data.amount - budget.amount)}.`,
          });
          return;
        }
      }
    }
    
    if (transactionToEdit) {
        editTransaction({ ...data, id: transactionToEdit.id, date: transactionToEdit.date });
        toast({ title: "Transaction updated!", description: "Your transaction has been successfully updated." });
    } else {
        addTransaction(data);
        toast({ title: "Transaction added!", description: "Your transaction has been successfully recorded." });
    }

    if(onFinished) onFinished();
  };

  const handleSuggestDescription = React.useCallback(async () => {
    setIsSuggesting(true);
    const { amount, category, walletId, type } = form.getValues();
    const wallet = wallets.find(w => w.id === walletId);
  
    if (!wallet || !category || !amount) {
      toast({
        variant: "destructive",
        title: "Missing Information",
        description: "Please fill in amount, category, and wallet before suggesting a description.",
      });
      setIsSuggesting(false);
      return;
    }
  
    try {
      const result = await suggestTransactionDescription({
        amount,
        category,
        wallet: wallet.name,
        transactionType: type,
        existingDescription: form.getValues('description'),
      });
      if (result.suggestedDescription) {
        form.setValue('description', result.suggestedDescription);
        toast({ title: "Description suggested!", description: "The AI has suggested a new description." });
      }
    } catch (error) {
      console.error("AI suggestion failed:", error);
      toast({
        variant: "destructive",
        title: "Suggestion Failed",
        description: "Could not get an AI-powered suggestion. Please try again.",
      });
    } finally {
      setIsSuggesting(false);
    }
  }, [form, wallets, toast]);

  const commonFields = (
    <>
      <FormField
        control={form.control}
        name="amount"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Amount</FormLabel>
            <FormControl>
              <Input type="number" step="0.01" placeholder="$0.00" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {activeTab !== 'transfer' && (
         <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input placeholder="e.g., Groceries" {...field} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
      )}
    </>
  );

  const expenseIncomeFields = (
    <>
      <FormField
        control={form.control}
        name="walletId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Wallet</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a wallet" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {wallets.map((wallet) => (
                  <SelectItem key={wallet.id} value={wallet.id}>
                    {wallet.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="category"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Category</FormLabel>
            <Popover>
                <PopoverTrigger asChild>
                    <FormControl>
                        <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                                "w-full justify-between",
                                !field.value && "text-muted-foreground"
                            )}
                        >
                            {field.value
                                ? categories.find(
                                    (category) => category.value === field.value
                                  )?.label
                                : "Select category"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                    </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                    <Command>
                        <CommandInput placeholder="Search category..." />
                        <CommandList>
                            <CommandEmpty>No category found.</CommandEmpty>
                            <CommandGroup>
                                {categories.filter(c => c.type === activeTab || c.type === 'all').map((category) => (
                                    <CommandItem
                                        value={category.label}
                                        key={category.value}
                                        onSelect={() => {
                                            form.setValue("category", category.value)
                                        }}
                                    >
                                        <Check
                                            className={cn(
                                                "mr-2 h-4 w-4",
                                                category.value === field.value
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                            )}
                                        />
                                        <category.icon className="mr-2 h-4 w-4" />
                                        {category.label}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );

  const transferFields = (
     <div className="space-y-4">
        <FormField
            control={form.control}
            name="walletId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>From</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select source wallet" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {wallets.map((wallet) => (
                      <SelectItem key={wallet.id} value={wallet.id}>
                        {wallet.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
        />
        <FormField
            control={form.control}
            name="toWalletId"
            render={({ field }) => (
                <FormItem>
                <FormLabel>To</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select destination wallet" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {wallets.map((wallet) => (
                      <SelectItem key={wallet.id} value={wallet.id}>
                        {wallet.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
        />
    </div>
  )

  const formContent = (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full flex flex-col h-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="expense" disabled={!!transactionToEdit}>Expense</TabsTrigger>
        <TabsTrigger value="income" disabled={!!transactionToEdit}>Income</TabsTrigger>
        <TabsTrigger value="transfer" disabled={!!transactionToEdit}>Transfer</TabsTrigger>
      </TabsList>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
          <div className="space-y-4 pt-4 pr-1">
            {commonFields}
            {activeTab === 'transfer' ? transferFields : expenseIncomeFields}
          </div>
          <div className="mt-auto pt-6">
            <Button type="submit" className="w-full">
                {transactionToEdit ? "Save Changes" : 
                    activeTab === 'income' ? "Add Income" :
                    activeTab === 'expense' ? "Add Expense" : "Transfer Funds"
                }
            </Button>
          </div>
        </form>
      </Form>
    </Tabs>
  );

  return (
    <div className="pt-2">
      {formContent}
    </div>
  );
}
