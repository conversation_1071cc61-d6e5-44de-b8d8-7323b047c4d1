export const __esModule: boolean;
export class JsiSkCanvas extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    drawRect(rect: any, paint: any): void;
    drawImage(image: any, x: any, y: any, paint: any): void;
    drawImageRect(img: any, src: any, dest: any, paint: any, fastSample: any): void;
    drawImageCubic(img: any, left: any, top: any, B: any, C: any, paint: any): void;
    drawImageOptions(img: any, left: any, top: any, fm: any, mm: any, paint: any): void;
    drawImageNine(img: any, center: any, dest: any, filter: any, paint: any): void;
    drawImageRectCubic(img: any, src: any, dest: any, B: any, C: any, paint: any): void;
    drawImageRectOptions(img: any, src: any, dest: any, fm: any, mm: any, paint: any): void;
    drawPaint(paint: any): void;
    drawLine(x0: any, y0: any, x1: any, y1: any, paint: any): void;
    drawCircle(cx: any, cy: any, radius: any, paint: any): void;
    drawVertices(verts: any, mode: any, paint: any): void;
    drawPatch(cubics: any, colors: any, texs: any, mode: any, paint: any): void;
    restoreToCount(saveCount: any): void;
    drawPoints(mode: any, points: any, paint: any): void;
    drawArc(oval: any, startAngle: any, sweepAngle: any, useCenter: any, paint: any): void;
    drawRRect(rrect: any, paint: any): void;
    drawDRRect(outer: any, inner: any, paint: any): void;
    drawOval(oval: any, paint: any): void;
    drawPath(path: any, paint: any): void;
    drawText(str: any, x: any, y: any, paint: any, font: any): void;
    drawTextBlob(blob: any, x: any, y: any, paint: any): void;
    drawGlyphs(glyphs: any, positions: any, x: any, y: any, font: any, paint: any): void;
    drawSvg(svg: any, _width: any, _height: any): void;
    save(): any;
    saveLayer(paint: any, bounds: any, backdrop: any, flags: any): any;
    restore(): void;
    rotate(rotationInDegrees: any, rx: any, ry: any): void;
    scale(sx: any, sy: any): void;
    skew(sx: any, sy: any): void;
    translate(dx: any, dy: any): void;
    drawColor(color: any, blendMode: any): void;
    clear(color: any): void;
    clipPath(path: any, op: any, doAntiAlias: any): void;
    clipRect(rect: any, op: any, doAntiAlias: any): void;
    clipRRect(rrect: any, op: any, doAntiAlias: any): void;
    concat(m: any): void;
    drawPicture(skp: any): void;
    drawAtlas(atlas: any, srcs: any, dsts: any, paint: any, blendMode: any, colors: any, sampling: any): void;
    readPixels(srcX: any, srcY: any, imageInfo: any): any;
}
import _Host = require("./Host");
