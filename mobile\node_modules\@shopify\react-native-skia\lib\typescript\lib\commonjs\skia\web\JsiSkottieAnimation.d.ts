export const __esModule: boolean;
export class Jsi<PERSON>kottieAnimation extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    getOpacityProps(): any;
    getTextProps(): any;
    getColorProps(): any;
    getTransformProps(): any;
    setColor(key: any, color: any): any;
    setText(key: any, text: any, size: any): any;
    setOpacity(key: any, opacity: any): any;
    setTransform(key: any, anchor: any, position: any, scale: any, rotation: any, skew: any, skewAxis: any): any;
    getSlotInfo(): any;
    setColorSlot(key: any, color: any): any;
    setScalarSlot(key: any, scalar: any): any;
    setVec2Slot(key: any, vec2: any): any;
    setTextSlot(key: any, text: any): any;
    setImageSlot(key: any, assetName: any): any;
    getColorSlot(key: any): any;
    getScalarSlot(key: any): any;
    getVec2Slot(key: any): {
        x: any;
        y: any;
    } | null;
    getTextSlot(key: any): {
        typeface: _JsiSkTypeface.JsiSkTypeface;
        text: any;
        textSize: any;
        minTextSize: any;
        maxTextSize: any;
        strokeWidth: any;
        lineHeight: any;
        lineShift: any;
        ascent: any;
        maxLines: any;
    };
    duration(): any;
    fps(): any;
    render(canvas: any, dstRect: any): void;
    seekFrame(frame: any, damageRect: any): void;
    size(): {
        width: any;
        height: any;
    };
    version(): any;
    dispose(): void;
}
import _Host = require("./Host");
import _JsiSkTypeface = require("./JsiSkTypeface");
