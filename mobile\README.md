# ExpenseFlow Mobile

A React Native Expo app for tracking expenses and income, built to match the functionality of the Next.js web version.

## Features

- **Authentication**: Email/password and Google OAuth login
- **Transaction Management**: Add, edit, and delete income/expense/transfer transactions
- **Wallet Management**: Multiple wallets with balance tracking
- **Category Management**: Organize transactions with custom categories
- **Budget Tracking**: Set and monitor spending budgets
- **Analytics**: Visual insights into spending patterns
- **Premium Subscription**: PayPal integration for premium features
- **Data Sync**: Real-time synchronization with Supabase

## Tech Stack

- **React Native** with Expo SDK 53
- **TypeScript** for type safety
- **React Navigation** for navigation
- **Supabase** for authentication and data storage
- **React Hook Form** with Zod validation
- **Victory Native** for charts and analytics
- **Expo Vector Icons** for consistent iconography

## Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Environment Configuration**:
   - Copy `.env.example` to `.env`
   - Add your Supabase URL and anon key
   - Add PayPal client ID for premium features

3. **Database Setup**:
   - Create a Supabase project
   - Run the SQL migration from the parent project's `MIGRATION_GUIDE.md`
   - Configure authentication providers (Google OAuth)

4. **Run the app**:
   ```bash
   # Start the development server
   npm start

   # Run on iOS simulator
   npm run ios

   # Run on Android emulator
   npm run android
   ```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Card, Input, Modal)
│   ├── AuthGuard.tsx   # Authentication wrapper
│   ├── TransactionForm.tsx
│   └── TransactionList.tsx
├── hooks/              # Custom React hooks
│   ├── useAuth.ts      # Authentication logic
│   └── useTransactions.ts # Data management
├── lib/                # Utilities and constants
│   ├── constants.ts    # App constants and initial data
│   ├── supabase.ts     # Supabase client configuration
│   ├── types.ts        # TypeScript type definitions
│   └── utils.ts        # Utility functions
├── navigation/         # Navigation configuration
│   └── AppNavigator.tsx
└── screens/            # Screen components
    ├── DashboardScreen.tsx
    ├── LoginScreen.tsx
    └── [other screens]
```

## Key Features Implementation

### Authentication
- Supabase Auth with email/password and Google OAuth
- Persistent sessions with AsyncStorage
- Auth guard for protected routes

### Data Management
- Real-time sync with Supabase
- Optimistic updates for better UX
- Free tier limitations enforcement
- Local state management with React hooks

### Transaction System
- Support for income, expense, and transfer transactions
- Category-based organization
- Wallet balance tracking
- Form validation with Zod

### UI/UX
- Native mobile design patterns
- Responsive layouts for different screen sizes
- Smooth animations and transitions
- Consistent iconography with Expo Vector Icons

## Environment Variables

```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id
```

## Development Notes

- The app maintains feature parity with the Next.js web version
- Uses the same Supabase database schema
- Implements the same business logic for transactions and wallets
- Follows React Native best practices for performance and UX

## Building for Production

1. **Configure app signing** (iOS/Android)
2. **Update app.json** with production values
3. **Build with EAS**:
   ```bash
   npx eas build --platform all
   ```

## Contributing

This mobile app is designed to mirror the functionality of the parent Next.js application. When adding features, ensure consistency between both platforms.
