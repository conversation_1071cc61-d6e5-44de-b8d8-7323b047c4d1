import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import AuthGuard from './src/components/AuthGuard';
import AppNavigator from './src/navigation/AppNavigator';

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar style="auto" />
      <AuthGuard>
        <AppNavigator />
      </AuthGuard>
    </SafeAreaProvider>
  );
}
