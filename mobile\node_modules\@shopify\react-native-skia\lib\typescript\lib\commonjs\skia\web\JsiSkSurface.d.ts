export const __esModule: boolean;
export class JsiSkSurface extends _Host.HostObject {
    constructor(CanvasKit: any, ref: any);
    flush(): void;
    width(): any;
    height(): any;
    getCanvas(): _JsiSkCanvas.JsiSkCanvas;
    makeImageSnapshot(bounds: any, outputImage: any): _JsiSkImage.JsiSkImage;
    getNativeTextureUnstable(): null;
}
import _Host = require("./Host");
import _JsiSkCanvas = require("./JsiSkCanvas");
import _JsiSkImage = require("./JsiSkImage");
