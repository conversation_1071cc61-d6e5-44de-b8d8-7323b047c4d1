import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialIcons } from '@expo/vector-icons';
import { View, TouchableOpacity, StyleSheet } from 'react-native';

import DashboardScreen from '../screens/DashboardScreen';
import AnalyticsScreen from '../screens/AnalyticsScreen';
import BudgetScreen from '../screens/BudgetScreen';
import WalletsScreen from '../screens/WalletsScreen';
import CategoriesScreen from '../screens/CategoriesScreen';
import SubscriptionScreen from '../screens/SubscriptionScreen';
import { useAuth } from '../hooks/useAuth';
import { authActions } from '../hooks/useAuth';

import type { MainTabParamList } from '../lib/types';

const Tab = createBottomTabNavigator<MainTabParamList>();

function LogoutButton() {
  const handleLogout = async () => {
    await authActions.signOut();
  };

  return (
    <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
      <MaterialIcons name="logout" size={24} color="#FF3B30" />
    </TouchableOpacity>
  );
}

export default function AppNavigator() {
  const { isSignedIn } = useAuth();

  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName: keyof typeof MaterialIcons.glyphMap;

            switch (route.name) {
              case 'Dashboard':
                iconName = 'dashboard';
                break;
              case 'Analytics':
                iconName = 'bar-chart';
                break;
              case 'Budget':
                iconName = 'target';
                break;
              case 'Wallets':
                iconName = 'account-balance-wallet';
                break;
              case 'Categories':
                iconName = 'category';
                break;
              case 'Subscription':
                iconName = 'star';
                break;
              default:
                iconName = 'help';
            }

            return <MaterialIcons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#007AFF',
          tabBarInactiveTintColor: '#8E8E93',
          tabBarStyle: {
            backgroundColor: '#fff',
            borderTopWidth: 1,
            borderTopColor: '#E5E5EA',
            paddingBottom: 5,
            paddingTop: 5,
            height: 60,
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
          },
          headerStyle: {
            backgroundColor: '#fff',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          },
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
            color: '#333',
          },
          headerRight: () => isSignedIn ? <LogoutButton /> : null,
        })}
      >
        <Tab.Screen 
          name="Dashboard" 
          component={DashboardScreen}
          options={{ title: 'Dashboard' }}
        />
        <Tab.Screen 
          name="Analytics" 
          component={AnalyticsScreen}
          options={{ title: 'Analytics' }}
        />
        <Tab.Screen 
          name="Budget" 
          component={BudgetScreen}
          options={{ title: 'Budget' }}
        />
        <Tab.Screen 
          name="Wallets" 
          component={WalletsScreen}
          options={{ title: 'Wallets' }}
        />
        <Tab.Screen 
          name="Categories" 
          component={CategoriesScreen}
          options={{ title: 'Categories' }}
        />
        <Tab.Screen 
          name="Subscription" 
          component={SubscriptionScreen}
          options={{ title: 'Subscription' }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  logoutButton: {
    marginRight: 16,
    padding: 4,
  },
});
