import type { ComponentType } from 'react';

export interface Wallet {
  id: string;
  name: string;
  icon: string; // Icon name from WALLET_ICONS
  balance: number;
  currency: string;
}

export type TransactionType = 'income' | 'expense' | 'transfer';

export interface Transaction {
  id: string;
  type: TransactionType;
  amount: number;
  category: string;
  description: string;
  walletId: string; // "from" wallet for transfers
  toWalletId?: string; // "to" wallet for transfers
  date: Date;
}

export interface CategoryInfo {
  value: string;
  label: string;
  iconName: string;
  type: TransactionType | 'all';
}

export interface Category extends Omit<CategoryInfo, 'iconName'> {
  icon: ComponentType<{ size?: number; color?: string }>;
}

export interface WalletIconInfo {
  name: string;
  icon: ComponentType<{ size?: number; color?: string }>;
}

export interface Budget {
  id: string;
  category: string;
  amount: number;
}

export interface Subscription {
  tier: 'free' | 'premium';
}

// Navigation types
export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Analytics: undefined;
  Budget: undefined;
  Wallets: undefined;
  Categories: undefined;
  Subscription: undefined;
};

// Form types
export interface TransactionFormData {
  type: TransactionType;
  amount: number;
  description: string;
  walletId: string;
  toWalletId?: string;
  category: string;
}

export interface WalletFormData {
  name: string;
  initialBalance: number;
  icon: string;
}

export interface BudgetFormData {
  category: string;
  amount: number;
}

export interface CategoryFormData {
  value: string;
  label: string;
  iconName: string;
  type: TransactionType | 'all';
}
