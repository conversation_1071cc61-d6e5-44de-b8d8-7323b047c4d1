export const __esModule: boolean;
export function processPaint({ opacity, color, strokeWidth, blendMode, style, strokeJoin, strokeCap, strokeMiter, antiAlias, dither, paint: paintRef }: {
    opacity: any;
    color: any;
    strokeWidth: any;
    blendMode: any;
    style: any;
    strokeJoin: any;
    strokeCap: any;
    strokeMiter: any;
    antiAlias: any;
    dither: any;
    paint: any;
}): {
    opacity: any;
    color: any;
    strokeWidth: any;
    blendMode: any;
    style: any;
    strokeJoin: any;
    strokeCap: any;
    strokeMiter: any;
    antiAlias: any;
    dither: any;
    paint: any;
} | null;
export function visit(recorder: any, root: any): void;
