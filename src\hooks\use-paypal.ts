"use client";

import * as React from "react";
import { useAuth } from "./use-auth";
import { toast } from "./use-toast";

export function usePayPal() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);

  const createPayPalOrder = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to upgrade your subscription.",
      });
      return null;
    }

    setIsLoading(true);

    try {
      // Create PayPal order for one-time payment
      const response = await fetch('/api/paypal/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
        }),
      });

      const { orderId, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      return orderId;
    } catch (error) {
      console.error('Error creating PayPal order:', error);
      toast({
        variant: "destructive",
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to create PayPal order.",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const capturePayPalOrder = async (orderId: string) => {
    try {
      const response = await fetch('/api/paypal/capture-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          userId: user?.id,
        }),
      });

      const { success, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (success) {
        toast({
          title: "Payment Successful!",
          description: "Welcome to ExpenseFlow Premium!",
        });
        // Redirect to success page
        window.location.href = `/subscription/success?order_id=${orderId}`;
      }

      return success;
    } catch (error) {
      console.error('Error capturing PayPal order:', error);
      toast({
        variant: "destructive",
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to complete payment.",
      });
      return false;
    }
  };

  const upgradeToPremium = () => {
    // This will be handled by the PayPal button component
    return createPayPalOrder();
  };

  return {
    isLoading,
    upgradeToPremium,
    createPayPalOrder,
    capturePayPalOrder,
  };
}
