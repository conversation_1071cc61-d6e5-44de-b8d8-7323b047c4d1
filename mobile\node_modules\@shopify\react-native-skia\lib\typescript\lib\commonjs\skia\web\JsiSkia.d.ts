export const __esModule: boolean;
export function JsiSkApi(CanvasKit: any): {
    Point: (x: any, y: any) => _JsiSkPoint.JsiSkPoint;
    RuntimeShaderBuilder: (_: any) => jest.Mock<any, any, any>;
    RRectXY: (rect: any, rx: any, ry: any) => _JsiSkRRect.JsiSkRRect;
    RSXform: (scos: any, ssin: any, tx: any, ty: any) => _JsiSkRSXform.JsiSkRSXform;
    RSXformFromRadians: (scale: any, r: any, tx: any, ty: any, px: any, py: any) => _JsiSkRSXform.JsiSkRSXform;
    Color: (color: any) => Float32Array<ArrayBufferLike>;
    ContourMeasureIter: (path: any, forceClosed: any, resScale: any) => _JsiSkContourMeasureIter.JsiSkContourMeasureIter;
    Paint: () => _JsiSkPaint.JsiSkPaint;
    PictureRecorder: () => _JsiSkPictureRecorder.JsiSkPictureRecorder;
    Picture: _JsiSkPictureFactory.JsiSkPictureFactory;
    Path: _JsiSkPathFactory.JsiSkPathFactory;
    Matrix: (matrix: any) => _JsiSkMatrix.JsiSkMatrix;
    ColorFilter: _JsiSkColorFilterFactory.JsiSkColorFilterFactory;
    Font: (typeface: any, size: any) => _JsiSkFont.JsiSkFont;
    Typeface: _JsiSkTypefaceFactory.JsiSkTypefaceFactory;
    MaskFilter: _JsiSkMaskFilterFactory.JsiSkMaskFilterFactory;
    RuntimeEffect: _JsiSkRuntimeEffectFactory.JsiSkRuntimeEffectFactory;
    ImageFilter: _JsiSkImageFilterFactory.JsiSkImageFilterFactory;
    Shader: _JsiSkShaderFactory.JsiSkShaderFactory;
    PathEffect: _JsiSkPathEffectFactory.JsiSkPathEffectFactory;
    MakeVertices: (mode?: any, positions?: any, textureCoordinates?: any, colors?: any, indices?: any, isVolatile?: any) => import("./JsiSkVertices").JsiSkVertices;
    Data: _JsiSkDataFactory.JsiSkDataFactory;
    Image: _JsiSkImageFactory.JsiSkImageFactory;
    AnimatedImage: _JsiSkAnimatedImageFactory.JsiSkAnimatedImageFactory;
    SVG: _JsiSkSVGFactory.JsiSkSVGFactory;
    TextBlob: _JsiSkTextBlobFactory.JsiSkTextBlobFactory;
    XYWHRect: (x: any, y: any, width: any, height: any) => _JsiSkRect.JsiSkRect;
    Surface: _JsiSkSurfaceFactory.JsiSkSurfaceFactory;
    TypefaceFontProvider: _JsiSkTypefaceFontProviderFactory.JsiSkTypefaceFontProviderFactory;
    FontMgr: _JsiSkFontMgrFactory.JsiSkFontMgrFactory;
    ParagraphBuilder: _JsiSkParagraphBuilderFactory.JsiSkParagraphBuilderFactory;
    NativeBuffer: _JsiSkNativeBufferFactory.JsiSkNativeBufferFactory;
    Skottie: _JsiSkottieFactory.JsiSkottieFactory;
    Video: (url?: any) => Promise<any>;
    Context: (_surface: any, _width: any, _height: any) => jest.Mock<any, any, any>;
    Recorder: () => jest.Mock<any, any, any>;
};
import _JsiSkPoint = require("./JsiSkPoint");
import _JsiSkRRect = require("./JsiSkRRect");
import _JsiSkRSXform = require("./JsiSkRSXform");
import _JsiSkContourMeasureIter = require("./JsiSkContourMeasureIter");
import _JsiSkPaint = require("./JsiSkPaint");
import _JsiSkPictureRecorder = require("./JsiSkPictureRecorder");
import _JsiSkPictureFactory = require("./JsiSkPictureFactory");
import _JsiSkPathFactory = require("./JsiSkPathFactory");
import _JsiSkMatrix = require("./JsiSkMatrix");
import _JsiSkColorFilterFactory = require("./JsiSkColorFilterFactory");
import _JsiSkFont = require("./JsiSkFont");
import _JsiSkTypefaceFactory = require("./JsiSkTypefaceFactory");
import _JsiSkMaskFilterFactory = require("./JsiSkMaskFilterFactory");
import _JsiSkRuntimeEffectFactory = require("./JsiSkRuntimeEffectFactory");
import _JsiSkImageFilterFactory = require("./JsiSkImageFilterFactory");
import _JsiSkShaderFactory = require("./JsiSkShaderFactory");
import _JsiSkPathEffectFactory = require("./JsiSkPathEffectFactory");
import _JsiSkDataFactory = require("./JsiSkDataFactory");
import _JsiSkImageFactory = require("./JsiSkImageFactory");
import _JsiSkAnimatedImageFactory = require("./JsiSkAnimatedImageFactory");
import _JsiSkSVGFactory = require("./JsiSkSVGFactory");
import _JsiSkTextBlobFactory = require("./JsiSkTextBlobFactory");
import _JsiSkRect = require("./JsiSkRect");
import _JsiSkSurfaceFactory = require("./JsiSkSurfaceFactory");
import _JsiSkTypefaceFontProviderFactory = require("./JsiSkTypefaceFontProviderFactory");
import _JsiSkFontMgrFactory = require("./JsiSkFontMgrFactory");
import _JsiSkParagraphBuilderFactory = require("./JsiSkParagraphBuilderFactory");
import _JsiSkNativeBufferFactory = require("./JsiSkNativeBufferFactory");
import _JsiSkottieFactory = require("./JsiSkottieFactory");
