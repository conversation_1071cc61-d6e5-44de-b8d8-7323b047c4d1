export const __esModule: boolean;
export class JsiSkPathFactory extends _Host.Host {
    Make(): _JsiSkPath.JsiSkPath;
    MakeFromSVGString(str: any): _JsiSkPath.JsiSkPath | null;
    MakeFromOp(one: any, two: any, op: any): _JsiSkPath.JsiSkPath | null;
    MakeFromCmds(cmds: any): _JsiSkPath.JsiSkPath | null;
    MakeFromText(_text: any, _x: any, _y: any, _font: any): jest.Mock<any, any, any>;
}
import _Host = require("./Host");
import _JsiSkPath = require("./JsiSkPath");
