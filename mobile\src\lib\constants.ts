import { MaterialIcons, FontAwesome5, Ionicons } from '@expo/vector-icons';
import type { Wallet, Transaction, Budget, CategoryInfo, WalletIconInfo } from './types';

// Wallet Icons
export const WALLET_ICONS: WalletIconInfo[] = [
  { name: 'wallet', icon: MaterialIcons },
  { name: 'credit-card', icon: MaterialIcons },
  { name: 'account-balance', icon: MaterialIcons },
  { name: 'savings', icon: MaterialIcons },
  { name: 'money', icon: FontAwesome5 },
  { name: 'piggy-bank', icon: FontAwesome5 },
  { name: 'university', icon: FontAwesome5 },
  { name: 'cash', icon: Ionicons },
  { name: 'card', icon: Ionicons },
];

// Category Icons
export const CATEGORY_ICONS = [
  { name: 'restaurant', icon: MaterialIcons },
  { name: 'local-grocery-store', icon: MaterialIcons },
  { name: 'directions-car', icon: MaterialIcons },
  { name: 'home', icon: MaterialIcons },
  { name: 'local-hospital', icon: MaterialIcons },
  { name: 'movie', icon: MaterialIcons },
  { name: 'school', icon: MaterialIcons },
  { name: 'shopping-cart', icon: MaterialIcons },
  { name: 'flight', icon: MaterialIcons },
  { name: 'fitness-center', icon: MaterialIcons },
  { name: 'phone', icon: MaterialIcons },
  { name: 'pets', icon: MaterialIcons },
  { name: 'work', icon: MaterialIcons },
  { name: 'attach-money', icon: MaterialIcons },
  { name: 'trending-up', icon: MaterialIcons },
];

// Initial Wallets
export const initialWallets: Wallet[] = [
  {
    id: 'wallet-1',
    name: 'Cash',
    icon: 'wallet',
    balance: 500,
    currency: 'USD',
  },
  {
    id: 'wallet-2',
    name: 'Bank Account',
    icon: 'account-balance',
    balance: 2500,
    currency: 'USD',
  },
];

// Initial Categories
export const initialCategories: CategoryInfo[] = [
  { value: 'food', label: 'Food & Dining', iconName: 'restaurant', type: 'expense' },
  { value: 'groceries', label: 'Groceries', iconName: 'local-grocery-store', type: 'expense' },
  { value: 'transport', label: 'Transportation', iconName: 'directions-car', type: 'expense' },
  { value: 'utilities', label: 'Utilities', iconName: 'home', type: 'expense' },
  { value: 'health', label: 'Healthcare', iconName: 'local-hospital', type: 'expense' },
  { value: 'entertainment', label: 'Entertainment', iconName: 'movie', type: 'expense' },
  { value: 'education', label: 'Education', iconName: 'school', type: 'expense' },
  { value: 'shopping', label: 'Shopping', iconName: 'shopping-cart', type: 'expense' },
  { value: 'travel', label: 'Travel', iconName: 'flight', type: 'expense' },
  { value: 'fitness', label: 'Fitness', iconName: 'fitness-center', type: 'expense' },
  { value: 'phone', label: 'Phone & Internet', iconName: 'phone', type: 'expense' },
  { value: 'pets', label: 'Pets', iconName: 'pets', type: 'expense' },
  { value: 'salary', label: 'Salary', iconName: 'work', type: 'income' },
  { value: 'freelance', label: 'Freelance', iconName: 'attach-money', type: 'income' },
  { value: 'investment', label: 'Investment', iconName: 'trending-up', type: 'income' },
];

// Initial Transactions
export const initialTransactions: Transaction[] = [
  {
    id: 'tx-1',
    type: 'expense',
    amount: 25.50,
    category: 'food',
    description: 'Lunch at restaurant',
    walletId: 'wallet-1',
    date: new Date(),
  },
  {
    id: 'tx-2',
    type: 'income',
    amount: 3000,
    category: 'salary',
    description: 'Monthly salary',
    walletId: 'wallet-2',
    date: new Date(),
  },
];

// Initial Budgets
export const initialBudgets: Budget[] = [];

// Free tier limits
export const FREE_TIER_LIMITS = {
  wallets: 3,
  categories: 5,
  historyMonths: 2,
};

// Chart colors
export const CHART_COLORS = [
  '#8884d8',
  '#82ca9d',
  '#ffc658',
  '#ff7c7c',
  '#8dd1e1',
  '#d084d0',
  '#ffb347',
  '#87ceeb',
  '#dda0dd',
  '#98fb98',
];
