
"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus, Save, Check, ChevronsUpDown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ICONS } from "@/lib/constants";
import type { Category, TransactionType } from "@/lib/types";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";

const categorySchema = z.object({
  label: z.string().min(1, "Category name is required."),
  value: z.string().min(1, "Category ID is required."),
  iconName: z.string().min(1, "Icon is required."),
  type: z.enum(["income", "expense", "all", "transfer"])
});

type CategoryFormValues = z.infer<typeof categorySchema>;

type CategorySaveData = Omit<Category, 'icon'> & {iconName: string};

interface CategoryDialogProps {
  onSave: (data: CategorySaveData) => void;
  categoryToEdit?: Category;
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CategoryDialog({ onSave, categoryToEdit, children, open, onOpenChange }: CategoryDialogProps) {
  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      label: "",
      value: "",
      iconName: ICONS[0].name,
      type: "expense"
    },
  });

  React.useEffect(() => {
    if (categoryToEdit && open) {
      form.reset({
        label: categoryToEdit.label,
        value: categoryToEdit.value,
        iconName: ICONS.find(i => {
            const IconComponent = i.icon as any;
            const CategoryIconComponent = categoryToEdit.icon as any;
            return IconComponent.displayName === CategoryIconComponent?.type?.displayName;
        })?.name || ICONS[0].name,
        type: categoryToEdit.type,
      });
    } else if (!categoryToEdit && open) {
      form.reset({
        label: "",
        value: "",
        iconName: ICONS[0].name,
        type: 'expense'
      });
    }
  }, [categoryToEdit, open, form]);
  
  const handleLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!categoryToEdit) {
      const value = e.target.value.toLowerCase().replace(/\s+/g, '-');
      form.setValue('value', value);
    }
    form.setValue('label', e.target.value);
  }

  const onSubmit = (data: CategoryFormValues) => {
    onSave(data);
    onOpenChange(false);
  };
  
  const selectedIcon = ICONS.find(i => i.name === form.watch('iconName'));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children}
      <DialogContent className="sm:max-w-[425px] bg-card-gradient">
        <DialogHeader>
          <DialogTitle>{categoryToEdit ? "Edit Category" : "Add New Category"}</DialogTitle>
          <DialogDescription>
            {categoryToEdit ? "Update your category's details." : "Create a new category for your transactions."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="label"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Groceries" {...field} onChange={handleLabelChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="value"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category ID</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., groceries" {...field} disabled={!!categoryToEdit} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="income">Income</SelectItem>
                      <SelectItem value="expense">Expense</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="iconName"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Icon</FormLabel>
                    <Popover>
                        <PopoverTrigger asChild>
                            <FormControl>
                                <Button
                                    variant="outline"
                                    role="combobox"
                                    className={cn(
                                        "w-full justify-between",
                                        !field.value && "text-muted-foreground"
                                    )}
                                >
                                    {selectedIcon ? (
                                        <div className="flex items-center gap-2">
                                            <selectedIcon.icon className="h-4 w-4" />
                                            {selectedIcon.name}
                                        </div>
                                    ) : (
                                        "Select icon"
                                    )}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                            <Command>
                                <CommandInput placeholder="Search icons..." />
                                <CommandList>
                                  <CommandEmpty>No icon found.</CommandEmpty>
                                  <CommandGroup>
                                      {ICONS.map((icon) => (
                                          <CommandItem
                                              value={icon.name}
                                              key={icon.name}
                                              onSelect={() => {
                                                  form.setValue("iconName", icon.name);
                                              }}
                                          >
                                              <Check
                                                  className={cn(
                                                      "mr-2 h-4 w-4",
                                                      field.value === icon.name ? "opacity-100" : "opacity-0"
                                                  )}
                                              />
                                              <icon.icon className="mr-2 h-4 w-4" />
                                              {icon.name}
                                          </CommandItem>
                                      ))}
                                  </CommandGroup>
                                </CommandList>
                            </Command>
                        </PopoverContent>
                    </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">
                {categoryToEdit ? <Save className="mr-2 h-4 w-4" /> : <Plus className="mr-2 h-4 w-4" />}
                {categoryToEdit ? "Save Changes" : "Add Category"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
