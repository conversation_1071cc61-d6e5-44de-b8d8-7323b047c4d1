# **App Name**: ExpenseFlow

## Core Features:

- Expense Entry: Capture expense details using a straightforward form with fields for wallet selection, category, description, and amount, enhanced with real-time currency formatting.
- Income Entry: Enable input of income transactions, mirroring the expense entry layout but with a distinct visual style to differentiate it from expenses.
- Transaction History: Implement a straightforward history view of all transactions, allowing users to filter transactions.
- Wallet Management: Implement wallet management functionality to add new wallets with options for icon selection, initial balance, and currency.
- AI Description Assistant: Leverage generative AI as a tool to provide auto-suggestions for transaction descriptions, making it easier for the user to quickly fill in expense/income forms.
- PWA Support: Progressive Web App (PWA) support for offline access and installability.

## Style Guidelines:

- Primary color: A vibrant electric blue (#0066FF), embodying innovation and modernity.
- Background color: A desaturated blue (#1A293D), offering a sophisticated and calm backdrop that allows the neon accents to stand out without overwhelming the user.
- Accent color: A neon pink (#FF0099) for interactive elements and highlights, creating a visually exciting experience.
- Body and headline font: 'Inter', a grotesque-style sans-serif, offers a modern, machined look.
- Neon-glowing icons representing expense categories.
- Cards with glassmorphic effects to provide depth.
- Use subtle micro-interactions for button taps, highlighting interaction affordance
- Ensure mobile responsiveness