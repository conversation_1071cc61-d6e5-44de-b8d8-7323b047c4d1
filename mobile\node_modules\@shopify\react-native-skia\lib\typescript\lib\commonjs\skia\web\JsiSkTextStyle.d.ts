export const __esModule: boolean;
export class JsiSkTextStyle {
    static toTextStyle(value: any): {
        backgroundColor: any;
        color: any;
        decoration: any;
        decorationColor: any;
        decorationStyle: {
            value: any;
        } | undefined;
        decorationThickness: any;
        fontFamilies: any;
        fontSize: any;
        fontStyle: {
            slant: {
                value: any;
            } | undefined;
            weight: {
                value: any;
            } | undefined;
            width: {
                value: any;
            } | undefined;
        } | undefined;
        fontFeatures: any;
        foregroundColor: any;
        fontVariations: any;
        halfLeading: any;
        heightMultiplier: any;
        letterSpacing: any;
        locale: any;
        shadows: any;
        textBaseline: {
            value: any;
        } | undefined;
        wordSpacing: any;
    };
}
