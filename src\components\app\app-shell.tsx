
"use client";

import * as React from "react";
import { createClient } from "@/utils/supabase/client";
import {
  BarChart2,
  LayoutGrid,
  Plus,
  CreditCard,
  Target,
  LayoutList,
  Star,
  LogOut,
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { Carousel, CarouselContent, CarouselItem, type CarouselApi } from "@/components/ui/carousel";


import DashboardPage from "@/components/app/dashboard-page";
import AnalyticsPage from "@/components/app/analytics-page";
import BudgetPage from "@/components/app/budget-page";
import WalletsPage from "@/components/app/wallets-page";
import CategoriesPage from "@/components/app/categories-page";
import SubscriptionPage from "@/components/app/subscription-page";
import { TransactionForm } from "./transaction-form";
import type { Transaction } from "@/lib/types";
import { signOut } from "@/app/login/actions";
import { useAuth } from "@/hooks/use-auth";

type NavItem = {
  id: 'dashboard' | 'analytics' | 'budget' | 'wallets' | 'categories' | 'subscription';
  label: string;
  icon: React.ElementType;
  component: React.ElementType;
}

const navItems: NavItem[] = [
  { id: "dashboard", label: "Dashboard", icon: LayoutGrid, component: DashboardPage },
  { id: "analytics", label: "Analytics", icon: BarChart2, component: AnalyticsPage },
  { id: "budget", label: "Budget", icon: Target, component: BudgetPage },
  { id: "wallets", label: "Wallets", icon: CreditCard, component: WalletsPage },
  { id: "categories", label: "Categories", icon: LayoutList, component: CategoriesPage },
  { id: "subscription", label: "Subscription", icon: Star, component: SubscriptionPage },
];

export function AppShell() {
  const isMobile = useIsMobile();
  const { user, isSignedIn } = useAuth();
  const [activeView, setActiveView] = React.useState<NavItem['id']>('dashboard');
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [transactionToEdit, setTransactionToEdit] = React.useState<Transaction | undefined>(undefined);
  const [api, setApi] = React.useState<CarouselApi>()
  const [isSignOutDialogOpen, setIsSignOutDialogOpen] = React.useState(false);
  const supabase = createClient();
  
  const handleEditTransaction = (transaction: Transaction) => {
    setTransactionToEdit(transaction);
    setIsFormOpen(true);
  };
  
  const handleAddTransaction = () => {
    setTransactionToEdit(undefined);
    setIsFormOpen(true);
  };

  const ActiveComponent = navItems.find(item => item.id === activeView)?.component || DashboardPage;
  const ActivePageComponent = React.createElement(ActiveComponent, { onEditTransaction: handleEditTransaction });

  React.useEffect(() => {
    if (!api) {
      return
    }
 
    const selectedIndex = navItems.findIndex(item => item.id === activeView);
    if (selectedIndex !== api.selectedScrollSnap()) {
      api.scrollTo(selectedIndex);
    }
    
  }, [api, activeView])

  React.useEffect(() => {
    if (!api) {
      return
    }
 
    api.on("select", () => {
      const newActiveViewId = navItems[api.selectedScrollSnap()].id;
      setActiveView(newActiveViewId);
    })
  }, [api])

  const mainContent = (
    <div className="flex min-h-screen w-full flex-col">
      <main className="flex w-[calc(100%-4rem)] flex-1 flex-col gap-4 p-4 pt-8 sm:p-6 md:p-8">
        {ActivePageComponent}
      </main>
    </div>
  );

  const fab = (
     <Button onClick={handleAddTransaction} className="fixed bottom-20 right-4 sm:bottom-6 sm:right-6 h-14 w-14 rounded-full shadow-lg z-20">
        <Plus className="h-6 w-6" />
        <span className="sr-only">Add Transaction</span>
     </Button>
  );

  const handleNavClick = (id: NavItem['id']) => {
    setActiveView(id);
  }

  const handleSignOutClick = () => {
    setIsSignOutDialogOpen(true);
  };

  const handleConfirmSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsSignOutDialogOpen(false);
    }
  };

  return (
    <>
      <div className={cn(!isMobile && "ml-16 w-full", isMobile && "pb-20")}>
        {!isMobile ? (
          mainContent
        ) : (
          <Carousel setApi={setApi} className="w-full h-full">
            <CarouselContent>
              {navItems.map((item) => {
                const MobileComponent = React.createElement(item.component, { onEditTransaction: handleEditTransaction });
                return (
                  <CarouselItem key={item.id}>
                    <div className="flex min-h-[calc(100vh-5rem)] w-full flex-col">
                      <main className="flex flex-1 flex-col gap-4 p-4 pt-8 sm:p-6 md:p-8">
                        {MobileComponent}
                      </main>
                    </div>
                  </CarouselItem>
                );
              })}
            </CarouselContent>
          </Carousel>
        )}
      </div>

      {!isMobile && (
        <aside className="fixed left-0 top-0 z-20 flex h-screen w-16 flex-col items-center justify-between border-r border-border/20 bg-card/10 backdrop-blur-lg py-4">
          <TooltipProvider delayDuration={0}>
            <nav className="flex flex-col items-center gap-4 mt-16">
              {navItems.map((item) => (
                <Tooltip key={item.id}>
                  <TooltipTrigger asChild>
                    <Button
                      variant={activeView === item.id ? "secondary" : "ghost"}
                      size="icon"
                      className="rounded-lg"
                      onClick={() => handleNavClick(item.id)}
                    >
                      <item.icon className="h-5 w-5" />
                      <span className="sr-only">{item.label}</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right" sideOffset={5}>
                    {item.label}
                  </TooltipContent>
                </Tooltip>
              ))}
            {isSignedIn && (
              <div className="flex flex-col items-center gap-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-lg text-muted-foreground hover:text-destructive"
                      onClick={handleSignOutClick}
                    >
                      <LogOut className="h-5 w-5 text-red-400" />
                      <span className="sr-only">Sign Out</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right" sideOffset={5}>
                    Sign Out
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
            </nav>
          </TooltipProvider>
        </aside>
      )}

      {fab}
      
      {isMobile && (
        <footer className="fixed bottom-0 z-10 w-full border-t border-border/20 bg-background/80 backdrop-blur-lg">
          <nav className={cn(
            "grid items-center justify-around p-2",
            isSignedIn ? "grid-cols-7" : "grid-cols-6"
          )}>
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavClick(item.id)}
                className={cn(
                  "flex flex-col items-center gap-1 rounded-md p-2 text-xs font-medium",
                  activeView === item.id
                    ? "text-primary"
                    : "text-muted-foreground hover:text-primary"
                )}
              >
                <item.icon className="h-6 w-6" />
                <span className="sr-only" >{item.label}</span>
              </button>
            ))}
            {isSignedIn && (
              <button
                onClick={handleSignOutClick}
                className="flex flex-col items-center gap-1 rounded-md p-2 text-xs font-medium text-muted-foreground hover:text-destructive"
              >
                <LogOut className="h-6 w-6 text-red-400" />
                <span className="sr-only" >Sign Out</span>
              </button>
            )}
          </nav>
        </footer>
      )}

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className={cn(isMobile && "h-screen bg-card-gradient", !isMobile && "sm:max-w-lg")}>
          <DialogHeader>
            <DialogTitle>{transactionToEdit ? 'Edit Transaction' : 'Add New Transaction'}</DialogTitle>
          </DialogHeader>
          <TransactionForm
            transactionToEdit={transactionToEdit}
            onFinished={() => setIsFormOpen(false)}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog open={isSignOutDialogOpen} onOpenChange={setIsSignOutDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Sign Out</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to sign out? You'll need to sign in again to access your account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmSignOut} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Sign Out
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
