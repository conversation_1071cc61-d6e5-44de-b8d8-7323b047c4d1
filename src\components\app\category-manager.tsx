
"use client";

import * as React from "react";
import { MoreVert<PERSON>, Edit, Trash2, PlusCircle, Crown } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTransactions } from "@/hooks/use-transactions";
import { Button } from "../ui/button";
import { CategoryDialog } from "./category-dialog";
import type { Category } from "@/lib/types";
import { Badge } from "../ui/badge";
import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "../ui/tooltip";

export function CategoryManager() {
  const { categories, addCategory, editCategory, deleteCategory, subscription } = useTransactions();
  const [selectedCategory, setSelectedCategory] = React.useState<Category | undefined>(undefined);
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);

  const handleEditClick = (category: Category) => {
    setSelectedCategory(category);
    setIsEditing(true);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (category: Category) => {
    if (category.type === 'transfer') return;
    setSelectedCategory(category);
    setIsDeleteOpen(true);
  };
  
  const handleAddClick = () => {
    setSelectedCategory(undefined);
    setIsEditing(false);
    setIsFormOpen(true);
  };

  const confirmDelete = () => {
    if (selectedCategory) {
      deleteCategory(selectedCategory.value);
    }
    setIsDeleteOpen(false);
    setSelectedCategory(undefined);
  };
  
  const handleSave = (data: Omit<Category, 'icon'> & { iconName: string }) => {
    if (isEditing) {
      editCategory(data);
    } else {
      addCategory(data);
    }
  };

  const sortedCategories = React.useMemo(() => {
    return [...categories].sort((a, b) => a.label.localeCompare(b.label));
  }, [categories]);
  
  const canAddCategory = subscription.tier === 'premium' || categories.length < 5;

  const AddButton = (
    <Button onClick={handleAddClick} disabled={!canAddCategory}>
      <PlusCircle className="mr-2 h-4 w-4" /> Add New Category
    </Button>
  );

  return (
    <>
      <Card className="bg-card-gradient">
        <CardHeader>
          <CardTitle>Manage Categories</CardTitle>
          <CardDescription>Customize your transaction categories.</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {sortedCategories.map((category) => {
            const Icon = category.icon;
            return (
              <Card key={category.value} className="relative bg-card/50">
                <CardHeader className="flex flex-row items-center gap-4 space-y-0 pb-6">
                    <div className="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-primary/10">
                        <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                        <CardTitle className="text-xl">{category.label}</CardTitle>
                        <Badge variant="outline" className="capitalize mt-1">{category.type}</Badge>
                    </div>
                </CardHeader>
                {category.type !== 'transfer' && (
                  <div className="absolute top-4 right-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditClick(category)}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(category)}
                          className="text-destructive text-red-400"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </Card>
            );
          })}
        </CardContent>
        <CardFooter className="flex flex-col items-center justify-center text-center p-6">
          {!canAddCategory ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>{AddButton}</TooltipTrigger>
                <TooltipContent>
                  <p className="flex items-center gap-2"><Crown className="h-4 w-4 text-yellow-400"/> Upgrade to Premium for unlimited categories.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            AddButton
          )}
        </CardFooter>
      </Card>

      <CategoryDialog
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        categoryToEdit={isEditing ? selectedCategory : undefined}
        onSave={handleSave}
      >
        <span />
      </CategoryDialog>
      
      <AlertDialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the category "{selectedCategory?.label}". This action cannot be undone. Transactions with this category will not be deleted but will need to be re-categorized.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
