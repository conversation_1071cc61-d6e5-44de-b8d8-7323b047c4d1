export const __esModule: boolean;
export class JsiSkColorFilterFactory extends _Host.Host {
    MakeMatrix(cMatrix: any): _JsiSkColorFilter.JsiSkColorFilter;
    MakeBlend(color: any, mode: any): _JsiSkColorFilter.JsiSkColorFilter;
    MakeCompose(outer: any, inner: any): _JsiSkColorFilter.JsiSkColorFilter;
    MakeLerp(t: any, dst: any, src: any): _JsiSkColorFilter.JsiSkColorFilter;
    MakeLinearToSRGBGamma(): _JsiSkColorFilter.JsiSkColorFilter;
    MakeSRGBToLinearGamma(): _JsiSkColorFilter.JsiSkColorFilter;
    MakeLumaColorFilter(): _JsiSkColorFilter.JsiSkColorFilter;
}
import _Host = require("./Host");
import _JsiSkColorFilter = require("./JsiSkColorFilter");
