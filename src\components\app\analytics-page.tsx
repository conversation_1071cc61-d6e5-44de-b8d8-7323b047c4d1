
"use client";

import * as React from "react";
import { format, subMonths, addMonths, isSameMonth } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { ExpenseChart } from "@/components/app/expense-chart";
import { CategorySpending } from "@/components/app/category-spending";
import { IncomeVsExpenseChart } from "@/components/app/income-vs-expense-chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTransactions } from "@/hooks/use-transactions";
import type { Transaction } from "@/lib/types";
import { Skeleton } from "../ui/skeleton";

export default function AnalyticsPage() {
  const { transactions, isInitialized } = useTransactions();
  const [currentMonth, setCurrentMonth] = React.useState(new Date());

  const monthlyTransactions = React.useMemo(() => {
    return transactions.filter(t => isSameMonth(t.date, currentMonth));
  }, [transactions, currentMonth]);
  
  const handlePrevMonth = () => {
    setCurrentMonth(prev => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(prev => addMonths(prev, 1));
  };

  const isNextMonthDisabled = isSameMonth(currentMonth, new Date());

  if (!isInitialized) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-48" />
        </div>
         <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
            <div className="xl:col-span-2">
                <Skeleton className="h-[300px] w-full" />
            </div>
            <Skeleton className="h-[300px] w-full" />
            <div className="lg:col-span-2 xl:col-span-3">
                 <Skeleton className="h-[400px] w-full" />
            </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Analytics</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={handlePrevMonth}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-lg font-semibold w-32 text-center">
            {format(currentMonth, "MMMM yyyy")}
          </span>
          <Button variant="outline" size="icon" onClick={handleNextMonth} disabled={isNextMonthDisabled}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
       <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
          <div className="xl:col-span-2">
              <IncomeVsExpenseChart transactions={monthlyTransactions} />
          </div>
          <CategorySpending transactions={monthlyTransactions} />
          <div className="lg:col-span-2 xl:col-span-3">
               <ExpenseChart transactions={monthlyTransactions} />
          </div>
      </div>
    </div>
  );
}
